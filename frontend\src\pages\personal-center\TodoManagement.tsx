import {
  CalendarOutlined,
  CheckOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Button,
  Col,
  Dropdown,
  Flex,
  Form,
  Grid,
  Input,
  Modal,
  Row,
  Space,
  Spin,
  Tabs,
  Tooltip,
  Typography,
  message,
} from 'antd';
import { ModalForm, ProList, ProCard, ProFormText, ProFormSelect } from '@ant-design/pro-components';
import React, { useEffect, useState, useMemo } from 'react';
import { TodoService } from '@/services/todo';
import type { TodoResponse, TodoStatsResponse } from '@/types/api';
import { usePagination } from '@/utils/paginationUtils';
import './TooltipFix.module.css'; // 导入 Tooltip 修复样式

const { Text } = Typography;
const { TabPane } = Tabs;

// 使用API类型定义，不需要重复定义接口
interface TodoManagementProps {
  onAddTodo?: (todo: TodoResponse) => void;
  onUpdateTodo?: (id: number, updatedTodo: Partial<TodoResponse>) => void;
  onDeleteTodo?: (id: number) => void;
}

const TodoManagement: React.FC<TodoManagementProps> = () => {
  /**
   * 响应式检测
   */
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();

  // TODO数据状态管理
  const [personalTasks, setPersonalTasks] = useState<TodoResponse[]>([]);
  const [todoStats, setTodoStats] = useState<TodoStatsResponse>({
    highPriorityCount: 0,
    mediumPriorityCount: 0,
    lowPriorityCount: 0,
    totalCount: 0,
    completedCount: 0,
    completionPercentage: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 待办事项状态管理
  const [todoModalVisible, setTodoModalVisible] = useState(false);
  const [todoForm] = Form.useForm();
  const [editingTodoId, setEditingTodoId] = useState<number | null>(null);

  // 过滤器状态
  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'completed'>(
    'pending',
  );
  const [searchText, setSearchText] = useState('');

  // 分页功能
  const { pagination, updateTotal } = usePagination({
    defaultPageSize: 10,
    pageSizeOptions: ['5', '10', '20', '50'],
    showTotal: (total, range) => `共 ${total} 条待办事项，显示第 ${range[0]}-${range[1]} 条`,
  });

  // 获取TODO数据
  useEffect(() => {
    const fetchTodoData = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('TodoManagement: 开始获取TODO数据');

        // 分别获取TODO列表和统计数据，避免一个失败影响另一个
        const todosPromise = TodoService.getUserTodos().catch((error) => {
          console.error('获取TODO列表失败:', error);
          return [];
        });

        const statsPromise = TodoService.getTodoStats().catch((error) => {
          console.error('获取TODO统计失败:', error);
          return {
            highPriorityCount: 0,
            mediumPriorityCount: 0,
            lowPriorityCount: 0,
            totalCount: 0,
            completedCount: 0,
            completionPercentage: 0,
          };
        });

        const [todos, stats] = await Promise.all([todosPromise, statsPromise]);

        console.log('TodoManagement: 获取到TODO列表:', todos);
        console.log('TodoManagement: 获取到统计数据:', stats);

        setPersonalTasks(todos);
        setTodoStats(stats);
      } catch (error) {
        console.error('获取TODO数据时发生未知错误:', error);
        setError('获取TODO数据失败，请刷新页面重试');
      } finally {
        setLoading(false);
      }
    };

    fetchTodoData();
  }, []);

  // 移除了taskCounts计算，因为标签页不再显示数量

  // 根据激活的标签和搜索文本过滤任务
  const filteredPersonalTasks = useMemo(() => {
    return (personalTasks || []).filter((task) => {
      // 根据标签过滤
      if (activeTab === 'pending' && task.status === 1) return false;
      if (activeTab === 'completed' && task.status === 0) return false;

      // 根据搜索文本过滤
      if (
        searchText &&
        !task.title.toLowerCase().includes(searchText.toLowerCase())
      ) {
        return false;
      }

      return true;
    });
  }, [personalTasks, activeTab, searchText]);

  // 更新总数
  React.useEffect(() => {
    updateTotal(filteredPersonalTasks.length);
  }, [filteredPersonalTasks.length, updateTotal]);

  // 处理待办事项操作
  const handleToggleTodoStatus = async (id: number) => {
    try {
      const task = personalTasks.find((t) => t.id === id);
      if (!task) {
        return;
      }

      const newStatus = task.status === 0 ? 1 : 0;

      await TodoService.updateTodo(id, { status: newStatus });

      // 更新本地状态
      setPersonalTasks(
        personalTasks.map((task) =>
          task.id === id ? { ...task, status: newStatus } : task,
        ),
      );

      // 刷新统计数据
      try {
        const stats = await TodoService.getTodoStats();
        setTodoStats(stats);
      } catch (statsError) {
        // 统计数据刷新失败不影响主要操作
      }
    } catch (error) {
      // 错误处理由响应拦截器统一处理
    }
  };

  const handleAddOrUpdateTodo = async (values: any) => {
    console.log('开始提交待办事项:', { values, editingTodoId });

    try {
      // 等待一小段时间以显示提交状态
      await new Promise(resolve => setTimeout(resolve, 500));

      if (editingTodoId) {
        // 更新现有待办事项
        console.log('更新待办事项:', editingTodoId, values);
        const updatedTodo = await TodoService.updateTodo(editingTodoId, {
          title: values.title,
          priority: values.priority,
        });
        console.log('更新成功:', updatedTodo);

        setPersonalTasks(
          personalTasks.map((task) =>
            task.id === editingTodoId ? updatedTodo : task,
          ),
        );
        message.success('任务更新成功！');
      } else {
        // 添加新待办事项
        console.log('创建新待办事项:', values);
        const newTodo = await TodoService.createTodo({
          title: values.title,
          priority: values.priority,
        });
        console.log('创建成功:', newTodo);

        setPersonalTasks([newTodo, ...personalTasks]);
        message.success('任务创建成功！');
      }

      // 刷新统计数据
      try {
        const stats = await TodoService.getTodoStats();
        setTodoStats(stats);
      } catch (statsError) {
        // 统计数据刷新失败不影响主要操作
        console.warn('刷新统计数据失败:', statsError);
      }

      // 重置编辑状态
      setEditingTodoId(null);

      console.log('待办事项操作完成');
      return true; // 返回true表示操作成功，ModalForm会自动关闭
    } catch (error) {
      // 错误处理由响应拦截器统一处理
      console.error('添加或更新待办事项失败:', error);
      return false; // 返回false表示操作失败，ModalForm不会关闭
    }
  };

  const handleDeleteTodo = async (id: number) => {
    // 获取要删除的任务信息
    const task = personalTasks.find((t) => t.id === id);
    const taskTitle = task?.title || '此待办事项';

    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除"${taskTitle}"吗？此操作无法撤销。`,
      okText: '确定删除',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          await TodoService.deleteTodo(id);
          setPersonalTasks(personalTasks.filter((task) => task.id !== id));

          // 刷新统计数据
          try {
            const stats = await TodoService.getTodoStats();
            setTodoStats(stats);
          } catch (statsError) {
            // 统计数据刷新失败不影响主要操作
          }
        } catch (error) {
          // 错误处理由响应拦截器统一处理
        }
      },
    });
  };

  return (
    <ProCard
      title={
        <Flex align="center" gap={8}>
          <CalendarOutlined style={{ fontSize: 18, color: '#2563eb' }} />
          <span style={{ color: '#1f2937', fontWeight: 600 }}>待办事项/任务列表</span>
        </Flex>
      }
      style={{
        borderRadius: 16,
        height: 'fit-content',
        minHeight: '500px', // 减少最小高度
        border: '1px solid rgba(37, 99, 235, 0.08)',
        background: 'linear-gradient(135deg, #ffffff 0%, #fafbff 100%)',
        boxShadow: '0 4px 20px rgba(37, 99, 235, 0.06)',
      }}
      headStyle={{
        borderBottom: '1px solid rgba(37, 99, 235, 0.08)',
        paddingBottom: 12,
        background: 'rgba(37, 99, 235, 0.02)',
      }}
      bodyStyle={{
        padding: screens.md ? '16px' : '12px',
      }}
    >
      {/* 搜索功能和添加新任务按钮 */}
      <Row
        gutter={screens.md ? [16, 0] : [12, 8]}
        style={{ marginBottom: screens.md ? 16 : 12 }}
      >
        <Col xs={24} sm={24} md={16} lg={18} xl={20}>
          <Input.Search
            placeholder="搜索任务..."
            allowClear
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: '100%' }}
            size={screens.md ? "middle" : "small"}
          />
        </Col>

        <Col xs={24} sm={24} md={8} lg={6} xl={4}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setTodoModalVisible(true);
            }}
            size={screens.md ? "middle" : "small"}
            block
          >
            {screens.md ? '添加任务' : '添加'}
          </Button>
        </Col>
      </Row>

      {/* 标签页 */}
      <Tabs
        activeKey={activeTab}
        onChange={(key) => setActiveTab(key as 'all' | 'pending' | 'completed')}
        size={screens.md ? "middle" : "small"}
        style={{ marginBottom: screens.md ? 8 : 6 }}
      >
        <TabPane tab="全部" key="all" />
        <TabPane tab="待处理" key="pending" />
        <TabPane tab="已完成" key="completed" />
      </Tabs>

    

      {/* 待办事项列表 */}
      {error ? (
        <Alert
          message="TODO数据加载失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      ) : (
        <Spin spinning={loading}>
          {/* 优先级统计 - 仅在待处理选项卡显示 */}
          {activeTab === 'pending' && (
            <Row gutter={[12, 12]} style={{ marginBottom: screens.md ? 16 : 12 }}>
              {/* 高优先级 */}
              <Col xs={24} sm={8} md={8} lg={8} xl={8}>
                <div
                  style={{
                    background: '#fff2f0',
                    border: '1px solid #ffccc7',
                    borderRadius: 8,
                    padding: screens.md ? '12px 16px' : '10px 12px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: screens.md ? 10 : 8,
                    height: '100%',
                    minHeight: screens.md ? 60 : 50,
                  }}
                >
                  <div
                    style={{
                      width: screens.md ? 10 : 8,
                      height: screens.md ? 10 : 8,
                      borderRadius: '50%',
                      background: '#ff4d4f',
                      flexShrink: 0,
                    }}
                  />
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <Text style={{
                      fontSize: screens.md ? 12 : 11,
                      color: '#8c8c8c',
                      display: 'block',
                      marginBottom: 2
                    }}>
                      高优先级
                    </Text>
                    <Text style={{
                      fontSize: screens.md ? 18 : 16,
                      fontWeight: 600,
                      color: '#cf1322',
                      display: 'block'
                    }}>
                      {todoStats.highPriorityCount}
                    </Text>
                  </div>
                </div>
              </Col>

              {/* 中优先级 */}
              <Col xs={24} sm={8} md={8} lg={8} xl={8}>
                <div
                  style={{
                    background: '#fffbe6',
                    border: '1px solid #ffe58f',
                    borderRadius: 8,
                    padding: screens.md ? '12px 16px' : '10px 12px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: screens.md ? 10 : 8,
                    height: '100%',
                    minHeight: screens.md ? 60 : 50,
                  }}
                >
                  <div
                    style={{
                      width: screens.md ? 10 : 8,
                      height: screens.md ? 10 : 8,
                      borderRadius: '50%',
                      background: '#faad14',
                      flexShrink: 0,
                    }}
                  />
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <Text style={{
                      fontSize: screens.md ? 12 : 11,
                      color: '#8c8c8c',
                      display: 'block',
                      marginBottom: 2
                    }}>
                      中优先级
                    </Text>
                    <Text style={{
                      fontSize: screens.md ? 18 : 16,
                      fontWeight: 600,
                      color: '#d48806',
                      display: 'block'
                    }}>
                      {todoStats.mediumPriorityCount}
                    </Text>
                  </div>
                </div>
              </Col>

              {/* 低优先级 */}
              <Col xs={24} sm={8} md={8} lg={8} xl={8}>
                <div
                  style={{
                    background: '#fafafa',
                    border: '1px solid #d9d9d9',
                    borderRadius: 8,
                    padding: screens.md ? '12px 16px' : '10px 12px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: screens.md ? 10 : 8,
                    height: '100%',
                    minHeight: screens.md ? 60 : 50,
                  }}
                >
                  <div
                    style={{
                      width: screens.md ? 10 : 8,
                      height: screens.md ? 10 : 8,
                      borderRadius: '50%',
                      background: '#8c8c8c',
                      flexShrink: 0,
                    }}
                  />
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <Text style={{
                      fontSize: screens.md ? 12 : 11,
                      color: '#8c8c8c',
                      display: 'block',
                      marginBottom: 2
                    }}>
                      低优先级
                    </Text>
                    <Text style={{
                      fontSize: screens.md ? 18 : 16,
                      fontWeight: 600,
                      color: '#595959',
                      display: 'block'
                    }}>
                      {todoStats.lowPriorityCount}
                    </Text>
                  </div>
                </div>
              </Col>
            </Row>
          )}
          <ProList
            dataSource={filteredPersonalTasks}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: filteredPersonalTasks.length,
              showSizeChanger: false, // 隐藏页面大小选择器
              showQuickJumper: false, // 隐藏快速跳转
              showTotal: undefined, // 隐藏总数显示
              onChange: pagination.onChange,
              simple: false, // 使用标准分页器，仅显示页码
            }}
            renderItem={(item) => {
              return (
                <div
                  className="todo-item"
                  style={{
                    padding: '12px 16px',
                    marginBottom: 8,
                    borderRadius: 12,
                    background: 'linear-gradient(135deg, #ffffff 0%, #fafbff 100%)',
                    opacity: item.status === 1 ? 0.7 : 1,
                    borderLeft: `4px solid ${
                      item.status === 1
                        ? '#059669'
                        : item.priority === 3
                          ? '#dc2626'
                          : item.priority === 2
                            ? '#d97706'
                            : '#64748b'
                    }`,
                    boxShadow: '0 2px 8px rgba(37, 99, 235, 0.04)',
                    border: '1px solid rgba(37, 99, 235, 0.06)',
                    transition: 'all 0.2s ease',
                  }}
                >
                  <Flex align="center" gap={12} style={{ width: '100%' }}>
                    {/* 左侧状态和优先级指示器 */}
                    <Flex vertical align="center">
                      {item.status === 1 ? (
                        <Flex
                          align="center"
                          justify="center"
                          style={{
                            width: 22,
                            height: 22,
                            borderRadius: '50%',
                            background: '#52c41a',
                          }}
                        >
                          <CheckOutlined
                            style={{ color: '#fff', fontSize: 12 }}
                          />
                        </Flex>
                      ) : (
                        <div
                          style={{
                            width: 18,
                            height: 18,
                            borderRadius: '50%',
                            border: `2px solid ${
                              item.priority === 3
                                ? '#ff4d4f'
                                : item.priority === 2
                                  ? '#faad14'
                                  : '#8c8c8c'
                            }`,
                          }}
                        />
                      )}

                      <div
                        style={{
                          width: 2,
                          height: 24,
                          background: '#f0f0f0',
                          marginTop: 4,
                        }}
                      />
                    </Flex>

                    {/* 任务信息区 */}
                    <Flex vertical style={{ flex: 1 }}>
                      <Text
                        style={{
                          fontSize: 14,
                          fontWeight: item.priority === 3 ? 500 : 'normal',
                          textDecoration:
                            item.status === 1 ? 'line-through' : 'none',
                          color: item.status === 1 ? '#8c8c8c' : '#262626',
                        }}
                      >
                        {item.title}
                      </Text>

                      {/* 显示创建日期 */}
                      <Space align="center" size={6} style={{ marginTop: 4 }}>
                        <CalendarOutlined
                          style={{
                            fontSize: 12,
                            color: '#8c8c8c',
                          }}
                        />
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          创建于:{' '}
                          {new Date(item.createdAt).toLocaleDateString('zh-CN')}
                        </Text>
                      </Space>
                    </Flex>

                    {/* 操作按钮区 */}
                    <Space size={screens.md ? 8 : 4}>
                      {/* 完成/未完成切换按钮 */}
                      <Tooltip title={item.status === 1 ? '标记未完成' : '标记完成'}>
                        <Button
                          type="text"
                          size="small"
                          icon={
                            <CheckOutlined
                              style={{
                                color: item.status === 1 ? '#8c8c8c' : '#52c41a',
                                fontSize: 14,
                              }}
                            />
                          }
                          onClick={() => handleToggleTodoStatus(item.id)}
                          style={{
                            width: screens.md ? 32 : 28,
                            height: screens.md ? 32 : 28,
                          }}
                        />
                      </Tooltip>

                      {/* 编辑按钮 */}
                      <Tooltip title="编辑任务">
                        <Button
                          type="text"
                          size="small"
                          icon={<EditOutlined style={{ color: '#8c8c8c', fontSize: 14 }} />}
                          onClick={() => {
                            setEditingTodoId(item.id);
                            todoForm.setFieldsValue({
                              title: item.title,
                              priority: item.priority,
                            });
                            setTodoModalVisible(true);
                          }}
                          style={{
                            width: screens.md ? 32 : 28,
                            height: screens.md ? 32 : 28,
                          }}
                        />
                      </Tooltip>

                      {/* 删除按钮 */}
                      <Tooltip title="删除任务">
                        <Button
                          type="text"
                          size="small"
                          icon={<DeleteOutlined style={{ color: '#ff4d4f', fontSize: 14 }} />}
                          onClick={() => handleDeleteTodo(item.id)}
                          style={{
                            width: screens.md ? 32 : 28,
                            height: screens.md ? 32 : 28,
                          }}
                        />
                      </Tooltip>
                    </Space>
                  </Flex>
                </div>
              );
            }}
          />

          {/* 待办事项表单模态框 */}
          <ModalForm<{
            title: string;
            priority: number;
          }>
            title={editingTodoId ? '编辑待办事项' : '新增待办事项'}
            open={todoModalVisible}
            onOpenChange={(visible) => {
              setTodoModalVisible(visible);
              if (!visible) {
                setEditingTodoId(null);
              }
            }} 
            modalProps={{
              centered: true,
              destroyOnClose: true,
              maskClosable: true,
              keyboard: true,
            }}
            submitTimeout={2000}
            onFinish={handleAddOrUpdateTodo}
   
            submitter={{
              searchConfig: {
                submitText: editingTodoId ? '更新任务' : '创建任务',
                resetText: '取消',
              },
              submitButtonProps: {
                icon: editingTodoId ? <EditOutlined /> : <PlusOutlined />,
              },
            }}
          >
            <ProFormText
              name="title"
              label="任务名称"
              placeholder="请输入任务名称"
              rules={[
                { required: true, message: '请输入任务名称' },
                { max: 100, message: '任务名称不能超过100个字符' },
                { whitespace: true, message: '任务名称不能为空白字符' }
              ]}
         
            />

            <ProFormSelect
              name="priority"
              label="优先级"
              placeholder="请选择优先级"
              initialValue={2}
              rules={[{ required: true, message: '请选择优先级' }]}
              options={[
                { value: 3, label: '高优先级' },
                { value: 2, label: '中优先级' },
                { value: 1, label: '低优先级' },
              ]}
         
            />
          </ModalForm>
        </Spin>
      )}
    </ProCard>
  );
};

export default TodoManagement;
