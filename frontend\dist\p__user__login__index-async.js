((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['p__user__login__index'],
{ "src/pages/user/login/index.tsx": function (module, exports, __mako_require__){
/**
 * 登录页面
 * 实现双阶段认证的第一阶段：账号登录
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _antdstyle = __mako_require__("node_modules/antd-style/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _components = __mako_require__("src/components/index.ts");
var _services = __mako_require__("src/services/index.ts");
var _defaultSettings = /*#__PURE__*/ _interop_require_default._(__mako_require__("config/defaultSettings.ts"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
var _s1 = $RefreshSig$();
const { Title, Text } = _antd.Typography;
// 登录表单组件（移到外部避免重新创建）
const LoginFormComponent = /*#__PURE__*/ _react.default.memo(_s(({ form, handleLogin, handleSendCode, sendingCode, countdown, loading })=>{
    _s();
    // 使用 useMemo 稳定按钮渲染，避免因倒计时变化导致输入框重新渲染
    const sendCodeButton = (0, _react.useMemo)(()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
            type: "link",
            size: "small",
            disabled: countdown > 0 || sendingCode,
            loading: sendingCode,
            onClick: handleSendCode,
            style: {
                padding: 0,
                height: 'auto'
            },
            children: countdown > 0 ? `${countdown}s后重发` : '发送验证码'
        }, void 0, false, {
            fileName: "src/pages/user/login/index.tsx",
            lineNumber: 41,
            columnNumber: 5
        }, this), [
        countdown,
        sendingCode,
        handleSendCode
    ]);
    // 使用 useMemo 稳定邮箱输入框，避免重新渲染
    const emailField = (0, _react.useMemo)(()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
            name: "email",
            rules: [
                {
                    required: true,
                    message: '请输入邮箱！'
                },
                {
                    type: 'email',
                    message: '请输入有效的邮箱地址！'
                }
            ],
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 65,
                    columnNumber: 17
                }, void 0),
                placeholder: "邮箱",
                autoComplete: "email"
            }, "email-input", false, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 63,
                columnNumber: 7
            }, this)
        }, "email-field", false, {
            fileName: "src/pages/user/login/index.tsx",
            lineNumber: 55,
            columnNumber: 5
        }, this), []);
    // 使用 useMemo 稳定验证码输入框，只在按钮变化时重新渲染
    const codeField = (0, _react.useMemo)(()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
            name: "code",
            rules: [
                {
                    required: true,
                    message: '请输入验证码！'
                },
                {
                    len: 6,
                    message: '验证码为6位数字！'
                },
                {
                    pattern: /^\d{6}$/,
                    message: '验证码只能包含数字！'
                }
            ],
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SafetyOutlined, {}, void 0, false, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 85,
                    columnNumber: 17
                }, void 0),
                placeholder: "6位验证码",
                maxLength: 6,
                suffix: sendCodeButton
            }, "code-input", false, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 83,
                columnNumber: 7
            }, this)
        }, "code-field", false, {
            fileName: "src/pages/user/login/index.tsx",
            lineNumber: 74,
            columnNumber: 5
        }, this), [
        sendCodeButton
    ]);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
        form: form,
        name: "login",
        size: "large",
        onFinish: handleLogin,
        autoComplete: "off",
        children: [
            emailField,
            codeField,
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginBottom: 16,
                    textAlign: 'center'
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    type: "secondary",
                    style: {
                        fontSize: '12px'
                    },
                    children: "新用户将自动完成注册并登录"
                }, void 0, false, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 106,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 105,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    type: "primary",
                    htmlType: "submit",
                    loading: loading,
                    block: true,
                    children: "登录 / 注册"
                }, void 0, false, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 112,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 111,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/user/login/index.tsx",
        lineNumber: 94,
        columnNumber: 5
    }, this);
}, "vH8AbKPV2dOQkAxU7xEfdsmzClM="));
_c = LoginFormComponent;
const useStyles = (0, _antdstyle.createStyles)(({ token })=>{
    return {
        container: {
            display: 'flex',
            flexDirection: 'column',
            height: '100vh',
            overflow: 'auto',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            position: 'relative',
            '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                zIndex: 0
            }
        },
        content: {
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '32px 16px',
            position: 'relative',
            zIndex: 1,
            '@media (max-width: 768px)': {
                padding: '24px 12px'
            },
            '@media (max-width: 480px)': {
                padding: '16px 8px'
            }
        },
        header: {
            marginBottom: 40,
            textAlign: 'center',
            '@media (max-width: 768px)': {
                marginBottom: 32
            },
            '@media (max-width: 480px)': {
                marginBottom: 24
            }
        },
        logo: {
            marginBottom: 16,
            '@media (max-width: 480px)': {
                marginBottom: 12,
                '& img': {
                    height: '40px !important'
                }
            }
        },
        title: {
            marginBottom: 0,
            '& h2': {
                color: 'white',
                fontSize: '32px',
                fontWeight: 600,
                marginBottom: '8px',
                '@media (max-width: 768px)': {
                    fontSize: '28px'
                },
                '@media (max-width: 480px)': {
                    fontSize: '24px'
                }
            },
            '& .ant-typography': {
                color: 'rgba(255, 255, 255, 0.9)',
                fontSize: '16px',
                '@media (max-width: 768px)': {
                    fontSize: '15px'
                },
                '@media (max-width: 480px)': {
                    fontSize: '14px'
                }
            }
        },
        loginCard: {
            width: '100%',
            maxWidth: 420,
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(20px)',
            borderRadius: '16px',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
            padding: '40px 32px',
            '@media (max-width: 768px)': {
                maxWidth: '380px',
                padding: '32px 24px',
                borderRadius: '12px'
            },
            '@media (max-width: 480px)': {
                maxWidth: '100%',
                margin: '0 8px',
                padding: '24px 20px',
                borderRadius: '8px'
            }
        },
        footer: {
            marginTop: 40,
            textAlign: 'center',
            '@media (max-width: 768px)': {
                marginTop: 32
            },
            '@media (max-width: 480px)': {
                marginTop: 24
            }
        },
        lang: {
            width: 42,
            height: 42,
            lineHeight: '42px',
            position: 'fixed',
            right: 16,
            top: 16,
            borderRadius: token.borderRadius,
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            color: 'white',
            zIndex: 10,
            '@media (max-width: 480px)': {
                width: 36,
                height: 36,
                lineHeight: '36px',
                right: 12,
                top: 12
            },
            ':hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.2)'
            }
        },
        formTitle: {
            textAlign: 'center',
            marginBottom: 32,
            '& h3': {
                color: '#2563eb',
                fontSize: '24px',
                fontWeight: 600,
                marginBottom: '8px',
                '@media (max-width: 480px)': {
                    fontSize: '20px'
                }
            },
            '& .ant-typography': {
                color: '#6b7280',
                fontSize: '14px'
            }
        }
    };
});
const LoginPage = ()=>{
    _s1();
    const [loading, setLoading] = (0, _react.useState)(false);
    const [sendingCode, setSendingCode] = (0, _react.useState)(false);
    const [countdown, setCountdown] = (0, _react.useState)(0);
    const [form] = _antd.Form.useForm(); // 将表单实例提升到父组件
    const { styles } = useStyles();
    const { setInitialState } = (0, _max.useModel)('@@initialState');
    // 使用 Form 内置的邮箱验证
    // 组件挂载时清除倒计时状态，避免页面刷新后无法输入
    (0, _react.useEffect)(()=>{
        setCountdown(0);
    }, []);
    // 倒计时效果
    _react.default.useEffect(()=>{
        let timer;
        if (countdown > 0) timer = setTimeout(()=>{
            setCountdown(countdown - 1);
        }, 1000);
        return ()=>{
            if (timer) clearTimeout(timer);
        };
    }, [
        countdown
    ]);
    // 发送验证码
    const handleSendCode = (0, _react.useCallback)(async (type = 'login')=>{
        let email;
        try {
            // 验证邮箱字段
            await form.validateFields([
                'email'
            ]);
            // 从表单获取邮箱值
            email = form.getFieldValue('email');
            if (!email) return;
        } catch (error) {
            // 表单验证失败，由表单验证规则处理错误显示
            return;
        }
        setSendingCode(true);
        try {
            const request = {
                email,
                type
            };
            const response = await _services.AuthService.sendVerificationCode(request);
            if (response.success) setCountdown(60); // 60秒倒计时
            else if (response.nextSendTime) setCountdown(response.nextSendTime);
        } catch (error) {
        // 错误处理由响应拦截器统一处理
        } finally{
            setSendingCode(false);
        }
    }, [
        form
    ]);
    // 处理登录/注册
    const handleLogin = (0, _react.useCallback)(async (values)=>{
        setLoading(true);
        try {
            const response = await _services.AuthService.login(values);
            // 登录成功后停止倒计时
            setCountdown(0);
            // 登录成功后，刷新 initialState
            await setInitialState((prevState)=>({
                    ...prevState,
                    currentUser: response.user,
                    currentTeam: response.teams.length > 0 ? response.teams[0] : undefined
                }));
            // 根据团队数量进行不同的跳转处理
            if (response.teams.length === 0) // 没有团队，跳转到个人中心页面
            _max.history.push('/personal-center');
            else // 有团队（无论一个还是多个），都跳转到个人中心整合页面
            _max.history.push('/personal-center', {
                teams: response.teams
            });
        } catch (error) {
        // 错误处理由响应拦截器统一处理
        } finally{
            setLoading(false);
        }
    }, [
        setInitialState
    ]);
    // 注册功能已移除，统一使用验证码登录/注册流程
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.container,
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.Helmet, {
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("title", {
                    children: [
                        "登录 / 注册",
                        _defaultSettings.default.title && ` - ${_defaultSettings.default.title}`
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 377,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 376,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                className: styles.content,
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        className: styles.header,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            direction: "vertical",
                            align: "center",
                            size: "large",
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.logo,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("img", {
                                        src: "/logo.svg",
                                        alt: "TeamAuth",
                                        height: 48
                                    }, void 0, false, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 386,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 385,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.title,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                            level: 2,
                                            children: "团队管理系统"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/login/index.tsx",
                                            lineNumber: 389,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            children: "现代化的团队协作与管理平台"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/login/index.tsx",
                                            lineNumber: 390,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 388,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 384,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 383,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        className: styles.loginCard,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                className: styles.formTitle,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                        level: 3,
                                        children: "欢迎回来"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 397,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "请使用邮箱验证码登录您的账户"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 398,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 396,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(LoginFormComponent, {
                                form: form,
                                handleLogin: handleLogin,
                                handleSendCode: ()=>handleSendCode('login'),
                                sendingCode: sendingCode,
                                countdown: countdown,
                                loading: loading
                            }, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 401,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                style: {
                                    margin: '24px 0',
                                    color: '#9ca3af'
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    style: {
                                        fontSize: '12px'
                                    },
                                    children: "安全登录"
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 411,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 410,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    textAlign: 'center'
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    style: {
                                        fontSize: '12px'
                                    },
                                    children: [
                                        "登录即表示您同意我们的",
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("a", {
                                            href: "#",
                                            style: {
                                                color: '#2563eb',
                                                marginLeft: 4,
                                                marginRight: 4
                                            },
                                            children: "服务条款"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/login/index.tsx",
                                            lineNumber: 419,
                                            columnNumber: 15
                                        }, this),
                                        "和",
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("a", {
                                            href: "#",
                                            style: {
                                                color: '#2563eb',
                                                marginLeft: 4
                                            },
                                            children: "隐私政策"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/login/index.tsx",
                                            lineNumber: 423,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 417,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 416,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 395,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        className: styles.footer,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 431,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 430,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 382,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/user/login/index.tsx",
        lineNumber: 375,
        columnNumber: 5
    }, this);
};
_s1(LoginPage, "Yq4Fb8fQ18048Y8KY3QhqRjuF9Y=", false, function() {
    return [
        _antd.Form.useForm,
        useStyles,
        _max.useModel
    ];
});
_c1 = LoginPage;
var _default = LoginPage;
var _c;
var _c1;
$RefreshReg$(_c, "LoginFormComponent");
$RefreshReg$(_c1, "LoginPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__user__login__index-async.js.map