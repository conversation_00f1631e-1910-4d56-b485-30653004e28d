/* Tooltip 修复样式 */

/* 确保 Tooltip 有足够高的 z-index */
:global(.ant-tooltip) {
  z-index: 1070 !important;
}

/* 确保 Tooltip 内容可见 */
:global(.ant-tooltip-inner) {
  background-color: rgba(0, 0, 0, 0.85) !important;
  color: #fff !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  padding: 8px 12px !important;
  border-radius: 6px !important;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 
              0 6px 16px 0 rgba(0, 0, 0, 0.08), 
              0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
  min-height: auto !important;
  min-width: auto !important;
  word-wrap: break-word !important;
  word-break: normal !important;
}

/* 确保 Tooltip 箭头可见 */
:global(.ant-tooltip-arrow) {
  z-index: 1071 !important;
}

/* 修复可能的定位问题 */
:global(.ant-tooltip-placement-top .ant-tooltip-arrow),
:global(.ant-tooltip-placement-topLeft .ant-tooltip-arrow),
:global(.ant-tooltip-placement-topRight .ant-tooltip-arrow) {
  bottom: -6px !important;
}

:global(.ant-tooltip-placement-bottom .ant-tooltip-arrow),
:global(.ant-tooltip-placement-bottomLeft .ant-tooltip-arrow),
:global(.ant-tooltip-placement-bottomRight .ant-tooltip-arrow) {
  top: -6px !important;
}

:global(.ant-tooltip-placement-left .ant-tooltip-arrow),
:global(.ant-tooltip-placement-leftTop .ant-tooltip-arrow),
:global(.ant-tooltip-placement-leftBottom .ant-tooltip-arrow) {
  right: -6px !important;
}

:global(.ant-tooltip-placement-right .ant-tooltip-arrow),
:global(.ant-tooltip-placement-rightTop .ant-tooltip-arrow),
:global(.ant-tooltip-placement-rightBottom .ant-tooltip-arrow) {
  left: -6px !important;
}

/* 确保 Tooltip 在所有容器之上 */
:global(.ant-tooltip-hidden) {
  display: none !important;
}

/* 修复可能的 overflow 问题 */
:global(.ant-tooltip) {
  max-width: 250px !important;
  word-wrap: break-word !important;
}

/* 确保在移动设备上也能正常显示 */
@media (max-width: 768px) {
  :global(.ant-tooltip) {
    max-width: 200px !important;
  }
  
  :global(.ant-tooltip-inner) {
    font-size: 13px !important;
    padding: 6px 10px !important;
  }
}

/* 修复可能的透明度问题 */
:global(.ant-tooltip) {
  opacity: 1 !important;
}

:global(.ant-tooltip-inner) {
  opacity: 1 !important;
}

/* 确保文本颜色对比度 */
:global(.ant-tooltip-inner) {
  color: #ffffff !important;
  text-shadow: none !important;
}

/* 修复可能的字体问题 */
:global(.ant-tooltip-inner) {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif !important;
  font-weight: 400 !important;
}
