((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['p__Dashboard__index'],
{ "src/pages/Dashboard/index.tsx": function (module, exports, __mako_require__){
/**
 * 仪表板页面
 *
 * 企业级仪表板页面，提供数据概览、快速操作和系统状态监控。
 * 采用响应式设计，适配不同屏幕尺寸，使用专业商务配色方案。
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
const { Title, Text, Paragraph } = _antd.Typography;
const Dashboard = ()=>{
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "仪表板",
        subTitle: "团队协作管理系统概览",
        extra: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.BarChartOutlined, {}, void 0, false, {
                    fileName: "src/pages/Dashboard/index.tsx",
                    lineNumber: 30,
                    columnNumber: 37
                }, void 0),
                children: "刷新数据"
            }, "refresh", false, {
                fileName: "src/pages/Dashboard/index.tsx",
                lineNumber: 30,
                columnNumber: 9
            }, void 0),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                    fileName: "src/pages/Dashboard/index.tsx",
                    lineNumber: 33,
                    columnNumber: 38
                }, void 0),
                type: "primary",
                children: "系统设置"
            }, "settings", false, {
                fileName: "src/pages/Dashboard/index.tsx",
                lineNumber: 33,
                columnNumber: 9
            }, void 0)
        ],
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            style: {
                padding: '0 24px'
            },
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                    style: {
                        marginBottom: 24,
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        color: 'white',
                        borderRadius: 16
                    },
                    bodyStyle: {
                        padding: '32px'
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                        gutter: [
                            24,
                            24
                        ],
                        align: "middle",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                xs: 24,
                                sm: 24,
                                md: 16,
                                lg: 18,
                                xl: 18,
                                xxl: 18,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    direction: "vertical",
                                    size: "small",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                            level: 2,
                                            style: {
                                                color: 'white',
                                                margin: 0
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DashboardOutlined, {
                                                    style: {
                                                        marginRight: 12
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 53,
                                                    columnNumber: 19
                                                }, this),
                                                "欢迎使用团队协作管理系统"
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 52,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                            style: {
                                                color: 'rgba(255, 255, 255, 0.9)',
                                                margin: 0,
                                                fontSize: 16
                                            },
                                            children: "高效管理团队，提升协作效率，让每个项目都能成功交付"
                                        }, void 0, false, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 56,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/Dashboard/index.tsx",
                                    lineNumber: 51,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/Dashboard/index.tsx",
                                lineNumber: 50,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                xs: 24,
                                sm: 24,
                                md: 8,
                                lg: 6,
                                xl: 6,
                                xxl: 6,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        textAlign: 'center'
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TrophyOutlined, {
                                        style: {
                                            fontSize: 64,
                                            color: 'rgba(255, 255, 255, 0.8)'
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/Dashboard/index.tsx",
                                        lineNumber: 63,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/Dashboard/index.tsx",
                                    lineNumber: 62,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/Dashboard/index.tsx",
                                lineNumber: 61,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/Dashboard/index.tsx",
                        lineNumber: 49,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/Dashboard/index.tsx",
                    lineNumber: 40,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                    gutter: [
                        16,
                        16
                    ],
                    style: {
                        marginBottom: 24
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 12,
                            sm: 12,
                            md: 6,
                            lg: 6,
                            xl: 6,
                            xxl: 6,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                statistic: {
                                    title: '团队数量',
                                    value: 8,
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                        style: {
                                            color: '#2563eb'
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/Dashboard/index.tsx",
                                        lineNumber: 76,
                                        columnNumber: 23
                                    }, void 0)
                                },
                                style: {
                                    background: 'linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%)',
                                    borderRadius: 12
                                }
                            }, void 0, false, {
                                fileName: "src/pages/Dashboard/index.tsx",
                                lineNumber: 72,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/Dashboard/index.tsx",
                            lineNumber: 71,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 12,
                            sm: 12,
                            md: 6,
                            lg: 6,
                            xl: 6,
                            xxl: 6,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                statistic: {
                                    title: '团队成员',
                                    value: 156,
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                        style: {
                                            color: '#059669'
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/Dashboard/index.tsx",
                                        lineNumber: 89,
                                        columnNumber: 23
                                    }, void 0)
                                },
                                style: {
                                    background: 'linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%)',
                                    borderRadius: 12
                                }
                            }, void 0, false, {
                                fileName: "src/pages/Dashboard/index.tsx",
                                lineNumber: 85,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/Dashboard/index.tsx",
                            lineNumber: 84,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 12,
                            sm: 12,
                            md: 6,
                            lg: 6,
                            xl: 6,
                            xxl: 6,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                statistic: {
                                    title: '活跃项目',
                                    value: 24,
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.FileTextOutlined, {
                                        style: {
                                            color: '#d97706'
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/Dashboard/index.tsx",
                                        lineNumber: 102,
                                        columnNumber: 23
                                    }, void 0)
                                },
                                style: {
                                    background: 'linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%)',
                                    borderRadius: 12
                                }
                            }, void 0, false, {
                                fileName: "src/pages/Dashboard/index.tsx",
                                lineNumber: 98,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/Dashboard/index.tsx",
                            lineNumber: 97,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 12,
                            sm: 12,
                            md: 6,
                            lg: 6,
                            xl: 6,
                            xxl: 6,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.StatisticCard, {
                                statistic: {
                                    title: '待处理任务',
                                    value: 42,
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.BellOutlined, {
                                        style: {
                                            color: '#dc2626'
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/Dashboard/index.tsx",
                                        lineNumber: 115,
                                        columnNumber: 23
                                    }, void 0)
                                },
                                style: {
                                    background: 'linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)',
                                    borderRadius: 12
                                }
                            }, void 0, false, {
                                fileName: "src/pages/Dashboard/index.tsx",
                                lineNumber: 111,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/Dashboard/index.tsx",
                            lineNumber: 110,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/Dashboard/index.tsx",
                    lineNumber: 70,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                    gutter: [
                        24,
                        24
                    ],
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 24,
                            md: 12,
                            lg: 8,
                            xl: 8,
                            xxl: 8,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                                title: "快速操作",
                                headerBordered: true,
                                style: {
                                    height: '100%',
                                    borderRadius: 12
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    direction: "vertical",
                                    size: "middle",
                                    style: {
                                        width: '100%'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                                fileName: "src/pages/Dashboard/index.tsx",
                                                lineNumber: 137,
                                                columnNumber: 25
                                            }, void 0),
                                            size: "large",
                                            block: true,
                                            style: {
                                                height: 48,
                                                borderRadius: 8
                                            },
                                            children: "创建新团队"
                                        }, void 0, false, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 135,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                fileName: "src/pages/Dashboard/index.tsx",
                                                lineNumber: 145,
                                                columnNumber: 25
                                            }, void 0),
                                            size: "large",
                                            block: true,
                                            style: {
                                                height: 48,
                                                borderRadius: 8
                                            },
                                            children: "邀请成员"
                                        }, void 0, false, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 144,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.FileTextOutlined, {}, void 0, false, {
                                                fileName: "src/pages/Dashboard/index.tsx",
                                                lineNumber: 153,
                                                columnNumber: 25
                                            }, void 0),
                                            size: "large",
                                            block: true,
                                            style: {
                                                height: 48,
                                                borderRadius: 8
                                            },
                                            children: "创建项目"
                                        }, void 0, false, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 152,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                            style: {
                                                margin: '12px 0'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 160,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                                                fileName: "src/pages/Dashboard/index.tsx",
                                                lineNumber: 162,
                                                columnNumber: 25
                                            }, void 0),
                                            size: "large",
                                            block: true,
                                            type: "dashed",
                                            style: {
                                                height: 48,
                                                borderRadius: 8
                                            },
                                            children: "系统设置"
                                        }, void 0, false, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 161,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/Dashboard/index.tsx",
                                    lineNumber: 134,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/Dashboard/index.tsx",
                                lineNumber: 129,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/Dashboard/index.tsx",
                            lineNumber: 128,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 24,
                            md: 12,
                            lg: 8,
                            xl: 8,
                            xxl: 8,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                                title: "最近活动",
                                headerBordered: true,
                                style: {
                                    height: '100%',
                                    borderRadius: 12
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    direction: "vertical",
                                    size: "middle",
                                    style: {
                                        width: '100%'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                padding: '12px 0',
                                                borderBottom: '1px solid #f0f0f0'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    strong: true,
                                                    children: "张三"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 183,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    type: "secondary",
                                                    style: {
                                                        marginLeft: 8
                                                    },
                                                    children: "加入了团队"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 184,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 185,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    type: "secondary",
                                                    style: {
                                                        fontSize: 12
                                                    },
                                                    children: "2 小时前"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 186,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 182,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                padding: '12px 0',
                                                borderBottom: '1px solid #f0f0f0'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    strong: true,
                                                    children: "李四"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 189,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    type: "secondary",
                                                    style: {
                                                        marginLeft: 8
                                                    },
                                                    children: "完成了任务"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 190,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 191,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    type: "secondary",
                                                    style: {
                                                        fontSize: 12
                                                    },
                                                    children: "4 小时前"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 192,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 188,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                padding: '12px 0',
                                                borderBottom: '1px solid #f0f0f0'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    strong: true,
                                                    children: "王五"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 195,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    type: "secondary",
                                                    style: {
                                                        marginLeft: 8
                                                    },
                                                    children: "创建了新项目"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 196,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 197,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    type: "secondary",
                                                    style: {
                                                        fontSize: 12
                                                    },
                                                    children: "1 天前"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 198,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 194,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                padding: '12px 0'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    strong: true,
                                                    children: "赵六"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 201,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    type: "secondary",
                                                    style: {
                                                        marginLeft: 8
                                                    },
                                                    children: "更新了文档"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 202,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 203,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    type: "secondary",
                                                    style: {
                                                        fontSize: 12
                                                    },
                                                    children: "2 天前"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 204,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 200,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/Dashboard/index.tsx",
                                    lineNumber: 181,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/Dashboard/index.tsx",
                                lineNumber: 176,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/Dashboard/index.tsx",
                            lineNumber: 175,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 24,
                            md: 24,
                            lg: 8,
                            xl: 8,
                            xxl: 8,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                                title: "系统状态",
                                headerBordered: true,
                                style: {
                                    height: '100%',
                                    borderRadius: 12
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    direction: "vertical",
                                    size: "middle",
                                    style: {
                                        width: '100%'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                display: 'flex',
                                                justifyContent: 'space-between',
                                                alignItems: 'center'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    children: "服务器状态"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 219,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        display: 'flex',
                                                        alignItems: 'center'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                width: 8,
                                                                height: 8,
                                                                borderRadius: '50%',
                                                                backgroundColor: '#059669',
                                                                marginRight: 8
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/Dashboard/index.tsx",
                                                            lineNumber: 221,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            type: "success",
                                                            children: "正常"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/Dashboard/index.tsx",
                                                            lineNumber: 228,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 220,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 218,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                display: 'flex',
                                                justifyContent: 'space-between',
                                                alignItems: 'center'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    children: "数据库连接"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 232,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        display: 'flex',
                                                        alignItems: 'center'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                width: 8,
                                                                height: 8,
                                                                borderRadius: '50%',
                                                                backgroundColor: '#059669',
                                                                marginRight: 8
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/Dashboard/index.tsx",
                                                            lineNumber: 234,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            type: "success",
                                                            children: "正常"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/Dashboard/index.tsx",
                                                            lineNumber: 241,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 233,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 231,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                display: 'flex',
                                                justifyContent: 'space-between',
                                                alignItems: 'center'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    children: "存储空间"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 245,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    children: "78% 已使用"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 246,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 244,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                display: 'flex',
                                                justifyContent: 'space-between',
                                                alignItems: 'center'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    children: "在线用户"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 249,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    strong: true,
                                                    style: {
                                                        color: '#2563eb'
                                                    },
                                                    children: "24 人"
                                                }, void 0, false, {
                                                    fileName: "src/pages/Dashboard/index.tsx",
                                                    lineNumber: 250,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 248,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                            style: {
                                                margin: '12px 0'
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 252,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                textAlign: 'center'
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                style: {
                                                    fontSize: 12
                                                },
                                                children: "最后更新：刚刚"
                                            }, void 0, false, {
                                                fileName: "src/pages/Dashboard/index.tsx",
                                                lineNumber: 254,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/Dashboard/index.tsx",
                                            lineNumber: 253,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/Dashboard/index.tsx",
                                    lineNumber: 217,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/Dashboard/index.tsx",
                                lineNumber: 212,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/Dashboard/index.tsx",
                            lineNumber: 211,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/Dashboard/index.tsx",
                    lineNumber: 126,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/Dashboard/index.tsx",
            lineNumber: 38,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/Dashboard/index.tsx",
        lineNumber: 26,
        columnNumber: 5
    }, this);
};
_c = Dashboard;
var _default = Dashboard;
var _c;
$RefreshReg$(_c, "Dashboard");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__Dashboard__index-async.js.map