{"version": 3, "sources": ["config/defaultSettings.ts"], "sourcesContent": ["import type { ProLayoutProps } from '@ant-design/pro-components';\n\nconst Settings: ProLayoutProps & {\n  pwa?: boolean;\n  logo?: string;\n} = {\n  navTheme: 'light',\n  colorPrimary: '#2563eb', // 更新为专业蓝色\n  layout: 'side',\n  contentWidth: 'Fluid',\n  fixedHeader: false,\n  fixSiderbar: true,\n  colorWeak: false,\n  title: '团队协作管理系统',\n  pwa: false,\n  logo: '/logo.svg',\n  iconfontUrl: '',\n  token: {\n    // 专业商务主题令牌\n    colorPrimary: '#2563eb',\n    colorSuccess: '#059669',\n    colorWarning: '#d97706',\n    colorError: '#dc2626',\n    colorInfo: '#0891b2',\n    colorText: '#1f2937',\n    colorTextSecondary: '#6b7280',\n    colorBgContainer: '#ffffff',\n    colorBgLayout: '#f8fafc',\n    borderRadius: 8,\n    borderRadiusLG: 12,\n  },\n};\n\nexport default Settings;\n"], "names": [], "mappings": ";;;;;;;4BAiCA;;;eAAA;;;;;;;;;;;;;AA/BA,MAAM,WAGF;IACF,UAAU;IACV,cAAc;IACd,QAAQ;IACR,cAAc;IACd,aAAa;IACb,aAAa;IACb,WAAW;IACX,OAAO;IACP,KAAK;IACL,MAAM;IACN,aAAa;IACb,OAAO;QACL,WAAW;QACX,cAAc;QACd,cAAc;QACd,cAAc;QACd,YAAY;QACZ,WAAW;QACX,WAAW;QACX,oBAAoB;QACpB,kBAAkB;QAClB,eAAe;QACf,cAAc;QACd,gBAAgB;IAClB;AACF;IAEA,WAAe"}