2025-08-05 11:30:03.781 [background-preinit] INFO  o.h.validator.internal.util.Version -- HV000001: Hibernate Validator 8.0.2.Final
2025-08-05 11:30:03.874 [main] INFO  c.teammanage.TeamManageApplication -- Starting TeamManageApplication using Java 17.0.8 with PID 11396 (H:\projects\IdeaProjects\teamAuth\backend\target\classes started by sweetotoro in H:\projects\IdeaProjects\teamAuth)
2025-08-05 11:30:03.992 [main] INFO  c.teammanage.TeamManageApplication -- No active profile set, falling back to 1 default profile: "default"
2025-08-05 11:30:09.436 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer -- Tomcat initialized with port 8080 (http)
2025-08-05 11:30:09.482 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Initializing ProtocolHandler ["http-nio-8080"]
2025-08-05 11:30:09.487 [main] INFO  o.a.catalina.core.StandardService -- Starting service [Tomcat]
2025-08-05 11:30:09.488 [main] INFO  o.a.catalina.core.StandardEngine -- Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-05 11:30:09.668 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] -- Initializing Spring embedded WebApplicationContext
2025-08-05 11:30:09.670 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext -- Root WebApplicationContext: initialization completed in 5569 ms
2025-08-05 11:30:14.005 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration -- 

Using generated security password: 3f8e7024-d999-4e8a-9059-9505dfd73a67

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-05 11:30:15.427 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware -- Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64dafeed
2025-08-05 11:30:16.042 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Starting ProtocolHandler ["http-nio-8080"]
2025-08-05 11:30:16.097 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer -- Tomcat started on port 8080 (http) with context path '/api/v1'
2025-08-05 11:30:16.128 [main] INFO  c.teammanage.TeamManageApplication -- Started TeamManageApplication in 14.049 seconds (process running for 21.67)
2025-08-05 11:30:16.150 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource -- HikariPool-1 - Starting...
2025-08-05 11:30:16.435 [scheduling-1] INFO  com.zaxxer.hikari.pool.HikariPool -- HikariPool-1 - Added connection org.mariadb.jdbc.Connection@5843480c
2025-08-05 11:30:16.441 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource -- HikariPool-1 - Start completed.
2025-08-05 11:30:16.505 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- ==>  Preparing: UPDATE team_invitation SET status = 'EXPIRED', updated_at = ? WHERE status = 'PENDING' AND expires_at <= ?
2025-08-05 11:30:16.725 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- ==> Parameters: 2025-08-05T11:30:16.453528300(LocalDateTime), 2025-08-05T11:30:16.453528300(LocalDateTime)
2025-08-05 11:30:16.787 [scheduling-1] DEBUG c.t.m.T.updateExpiredInvitations -- <==    Updates: 0
