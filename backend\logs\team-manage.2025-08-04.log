2025-08-04 17:26:02.523 [background-preinit] INFO  o.h.validator.internal.util.Version -- HV000001: Hibernate Validator 8.0.2.Final
2025-08-04 17:26:02.644 [main] INFO  c.teammanage.TeamManageApplication -- Starting TeamManageApplication using Java 17.0.8 with PID 16080 (H:\projects\IdeaProjects\teamAuth\backend\target\classes started by sweetotoro in H:\projects\IdeaProjects\teamAuth\backend)
2025-08-04 17:26:02.647 [main] INFO  c.teammanage.TeamManageApplication -- No active profile set, falling back to 1 default profile: "default"
2025-08-04 17:26:05.621 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer -- Tomcat initialized with port 8080 (http)
2025-08-04 17:26:05.648 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Initializing ProtocolHandler ["http-nio-8080"]
2025-08-04 17:26:05.653 [main] INFO  o.a.catalina.core.StandardService -- Starting service [Tomcat]
2025-08-04 17:26:05.654 [main] INFO  o.a.catalina.core.StandardEngine -- Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-04 17:26:05.894 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] -- Initializing Spring embedded WebApplicationContext
2025-08-04 17:26:05.895 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext -- Root WebApplicationContext: initialization completed in 3133 ms
2025-08-04 17:26:08.345 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration -- 

Using generated security password: aa3d6c92-c14a-4593-bc24-6b2748440449

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-04 17:26:08.925 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware -- Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@160ac7fb
2025-08-04 17:26:09.262 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Starting ProtocolHandler ["http-nio-8080"]
2025-08-04 17:26:09.301 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext -- Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-04 17:26:09.339 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger -- 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-04 17:26:09.371 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter -- 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-08-04 17:27:49.992 [background-preinit] INFO  o.h.validator.internal.util.Version -- HV000001: Hibernate Validator 8.0.2.Final
2025-08-04 17:27:50.106 [main] INFO  c.teammanage.TeamManageApplication -- Starting TeamManageApplication using Java 17.0.8 with PID 22500 (H:\projects\IdeaProjects\teamAuth\backend\target\classes started by sweetotoro in H:\projects\IdeaProjects\teamAuth\backend)
2025-08-04 17:27:50.109 [main] INFO  c.teammanage.TeamManageApplication -- No active profile set, falling back to 1 default profile: "default"
2025-08-04 17:27:53.324 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer -- Tomcat initialized with port 8080 (http)
2025-08-04 17:27:53.360 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Initializing ProtocolHandler ["http-nio-8080"]
2025-08-04 17:27:53.367 [main] INFO  o.a.catalina.core.StandardService -- Starting service [Tomcat]
2025-08-04 17:27:53.371 [main] INFO  o.a.catalina.core.StandardEngine -- Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-04 17:27:53.514 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] -- Initializing Spring embedded WebApplicationContext
2025-08-04 17:27:53.515 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext -- Root WebApplicationContext: initialization completed in 3284 ms
2025-08-04 17:27:56.015 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration -- 

Using generated security password: 9725e609-aadc-4754-9d5d-b069c62a545e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-04 17:27:56.537 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware -- Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@160ac7fb
2025-08-04 17:27:56.876 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Starting ProtocolHandler ["http-nio-8080"]
2025-08-04 17:27:56.889 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext -- Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-04 17:27:56.924 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger -- 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-04 17:27:56.955 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter -- 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

