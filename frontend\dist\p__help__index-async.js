((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['p__help__index'],
{ "src/pages/help/index.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
const { Title, Paragraph, Text } = _antd.Typography;
const { Step } = _antd.Steps;
const HelpPage = ()=>{
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "帮助中心",
        subTitle: "团队协作管理系统使用指南",
        extra: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                type: "primary",
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PhoneOutlined, {}, void 0, false, {
                    fileName: "src/pages/help/index.tsx",
                    lineNumber: 26,
                    columnNumber: 52
                }, void 0),
                children: "联系技术支持"
            }, "contact", false, {
                fileName: "src/pages/help/index.tsx",
                lineNumber: 26,
                columnNumber: 9
            }, void 0)
        ],
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            style: {
                padding: '0 24px'
            },
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                    gutter: [
                        16,
                        16
                    ],
                    style: {
                        marginBottom: 32
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 12,
                            sm: 8,
                            md: 6,
                            lg: 4,
                            xl: 4,
                            xxl: 4,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                hoverable: true,
                                style: {
                                    textAlign: 'center',
                                    borderRadius: 12
                                },
                                bodyStyle: {
                                    padding: '24px 16px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.BookOutlined, {
                                        style: {
                                            fontSize: 32,
                                            color: '#2563eb',
                                            marginBottom: 12
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 40,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: "快速开始"
                                        }, void 0, false, {
                                            fileName: "src/pages/help/index.tsx",
                                            lineNumber: 42,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 41,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 35,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/help/index.tsx",
                            lineNumber: 34,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 12,
                            sm: 8,
                            md: 6,
                            lg: 4,
                            xl: 4,
                            xxl: 4,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                hoverable: true,
                                style: {
                                    textAlign: 'center',
                                    borderRadius: 12
                                },
                                bodyStyle: {
                                    padding: '24px 16px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                        style: {
                                            fontSize: 32,
                                            color: '#059669',
                                            marginBottom: 12
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 52,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: "团队管理"
                                        }, void 0, false, {
                                            fileName: "src/pages/help/index.tsx",
                                            lineNumber: 54,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 53,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 47,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/help/index.tsx",
                            lineNumber: 46,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 12,
                            sm: 8,
                            md: 6,
                            lg: 4,
                            xl: 4,
                            xxl: 4,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                hoverable: true,
                                style: {
                                    textAlign: 'center',
                                    borderRadius: 12
                                },
                                bodyStyle: {
                                    padding: '24px 16px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                        style: {
                                            fontSize: 32,
                                            color: '#d97706',
                                            marginBottom: 12
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 64,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: "用户设置"
                                        }, void 0, false, {
                                            fileName: "src/pages/help/index.tsx",
                                            lineNumber: 66,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 65,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 59,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/help/index.tsx",
                            lineNumber: 58,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 12,
                            sm: 8,
                            md: 6,
                            lg: 4,
                            xl: 4,
                            xxl: 4,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                hoverable: true,
                                style: {
                                    textAlign: 'center',
                                    borderRadius: 12
                                },
                                bodyStyle: {
                                    padding: '24px 16px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {
                                        style: {
                                            fontSize: 32,
                                            color: '#dc2626',
                                            marginBottom: 12
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 76,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: "系统设置"
                                        }, void 0, false, {
                                            fileName: "src/pages/help/index.tsx",
                                            lineNumber: 78,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 77,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 71,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/help/index.tsx",
                            lineNumber: 70,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 12,
                            sm: 8,
                            md: 6,
                            lg: 4,
                            xl: 4,
                            xxl: 4,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                hoverable: true,
                                style: {
                                    textAlign: 'center',
                                    borderRadius: 12
                                },
                                bodyStyle: {
                                    padding: '24px 16px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SafetyOutlined, {
                                        style: {
                                            fontSize: 32,
                                            color: '#7c3aed',
                                            marginBottom: 12
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 88,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: "安全中心"
                                        }, void 0, false, {
                                            fileName: "src/pages/help/index.tsx",
                                            lineNumber: 90,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 89,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 83,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/help/index.tsx",
                            lineNumber: 82,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 12,
                            sm: 8,
                            md: 6,
                            lg: 4,
                            xl: 4,
                            xxl: 4,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                hoverable: true,
                                style: {
                                    textAlign: 'center',
                                    borderRadius: 12
                                },
                                bodyStyle: {
                                    padding: '24px 16px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.QuestionCircleOutlined, {
                                        style: {
                                            fontSize: 32,
                                            color: '#0891b2',
                                            marginBottom: 12
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 100,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: "常见问题"
                                        }, void 0, false, {
                                            fileName: "src/pages/help/index.tsx",
                                            lineNumber: 102,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 101,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 95,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/help/index.tsx",
                            lineNumber: 94,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/help/index.tsx",
                    lineNumber: 33,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                    gutter: [
                        24,
                        24
                    ],
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 24,
                            md: 16,
                            lg: 16,
                            xl: 16,
                            xxl: 16,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                direction: "vertical",
                                size: "large",
                                style: {
                                    width: '100%'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                                        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.BookOutlined, {
                                                    style: {
                                                        color: '#2563eb'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 116,
                                                    columnNumber: 21
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                    children: "快速开始"
                                                }, void 0, false, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 117,
                                                    columnNumber: 21
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/help/index.tsx",
                                            lineNumber: 115,
                                            columnNumber: 19
                                        }, void 0),
                                        headerBordered: true,
                                        style: {
                                            borderRadius: 12
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                                style: {
                                                    fontSize: 16,
                                                    marginBottom: 24
                                                },
                                                children: "欢迎使用团队协作管理系统！本系统帮助您高效管理团队成员、项目和任务。"
                                            }, void 0, false, {
                                                fileName: "src/pages/help/index.tsx",
                                                lineNumber: 123,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                level: 4,
                                                style: {
                                                    marginTop: 24
                                                },
                                                children: "首次使用步骤"
                                            }, void 0, false, {
                                                fileName: "src/pages/help/index.tsx",
                                                lineNumber: 127,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Steps, {
                                                direction: "vertical",
                                                size: "small",
                                                current: -1,
                                                style: {
                                                    marginTop: 16
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Step, {
                                                        title: "注册账号并登录系统",
                                                        description: "使用邮箱注册账号，验证后即可登录使用",
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 137,
                                                            columnNumber: 27
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/help/index.tsx",
                                                        lineNumber: 134,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Step, {
                                                        title: "创建或加入团队",
                                                        description: "创建新团队或通过邀请链接加入现有团队",
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 142,
                                                            columnNumber: 27
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/help/index.tsx",
                                                        lineNumber: 139,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Step, {
                                                        title: "邀请团队成员",
                                                        description: "通过邮箱邀请同事加入您的团队",
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 147,
                                                            columnNumber: 27
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/help/index.tsx",
                                                        lineNumber: 144,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Step, {
                                                        title: "开始协作管理",
                                                        description: "创建项目、分配任务，开始高效协作",
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.RightOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 152,
                                                            columnNumber: 27
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/help/index.tsx",
                                                        lineNumber: 149,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/help/index.tsx",
                                                lineNumber: 128,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 113,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                                        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                    style: {
                                                        color: '#059669'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 161,
                                                    columnNumber: 21
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                    children: "主要功能"
                                                }, void 0, false, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 162,
                                                    columnNumber: 21
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/help/index.tsx",
                                            lineNumber: 160,
                                            columnNumber: 19
                                        }, void 0),
                                        headerBordered: true,
                                        style: {
                                            borderRadius: 12
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                            gutter: [
                                                16,
                                                16
                                            ],
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                    xs: 24,
                                                    sm: 12,
                                                    md: 12,
                                                    lg: 12,
                                                    xl: 12,
                                                    xxl: 12,
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            padding: '16px 0'
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                                level: 5,
                                                                style: {
                                                                    color: '#2563eb',
                                                                    marginBottom: 8
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                                        style: {
                                                                            marginRight: 8
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/help/index.tsx",
                                                                        lineNumber: 172,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    "团队管理"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/help/index.tsx",
                                                                lineNumber: 171,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                                                style: {
                                                                    marginBottom: 0,
                                                                    color: '#6b7280'
                                                                },
                                                                children: "创建团队、邀请成员、设置权限，构建高效协作团队"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/help/index.tsx",
                                                                lineNumber: 175,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/help/index.tsx",
                                                        lineNumber: 170,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 169,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                    xs: 24,
                                                    sm: 12,
                                                    md: 12,
                                                    lg: 12,
                                                    xl: 12,
                                                    xxl: 12,
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            padding: '16px 0'
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                                level: 5,
                                                                style: {
                                                                    color: '#2563eb',
                                                                    marginBottom: 8
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.BookOutlined, {
                                                                        style: {
                                                                            marginRight: 8
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/help/index.tsx",
                                                                        lineNumber: 183,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    "项目协作"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/help/index.tsx",
                                                                lineNumber: 182,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                                                style: {
                                                                    marginBottom: 0,
                                                                    color: '#6b7280'
                                                                },
                                                                children: "创建项目、分配任务、跟踪进度，确保项目顺利交付"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/help/index.tsx",
                                                                lineNumber: 186,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/help/index.tsx",
                                                        lineNumber: 181,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 180,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                    xs: 24,
                                                    sm: 12,
                                                    md: 12,
                                                    lg: 12,
                                                    xl: 12,
                                                    xxl: 12,
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            padding: '16px 0'
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                                level: 5,
                                                                style: {
                                                                    color: '#2563eb',
                                                                    marginBottom: 8
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SafetyOutlined, {
                                                                        style: {
                                                                            marginRight: 8
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/help/index.tsx",
                                                                        lineNumber: 194,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    "文档管理"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/help/index.tsx",
                                                                lineNumber: 193,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                                                style: {
                                                                    marginBottom: 0,
                                                                    color: '#6b7280'
                                                                },
                                                                children: "上传文档、版本控制、共享协作，知识管理更轻松"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/help/index.tsx",
                                                                lineNumber: 197,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/help/index.tsx",
                                                        lineNumber: 192,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 191,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                    xs: 24,
                                                    sm: 12,
                                                    md: 12,
                                                    lg: 12,
                                                    xl: 12,
                                                    xxl: 12,
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            padding: '16px 0'
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                                level: 5,
                                                                style: {
                                                                    color: '#2563eb',
                                                                    marginBottom: 8
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {
                                                                        style: {
                                                                            marginRight: 8
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/help/index.tsx",
                                                                        lineNumber: 205,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    "消息通知"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/help/index.tsx",
                                                                lineNumber: 204,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                                                style: {
                                                                    marginBottom: 0,
                                                                    color: '#6b7280'
                                                                },
                                                                children: "实时消息、邮件提醒、状态更新，保持团队同步"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/help/index.tsx",
                                                                lineNumber: 208,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/help/index.tsx",
                                                        lineNumber: 203,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 202,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/help/index.tsx",
                                            lineNumber: 168,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 158,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 111,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/help/index.tsx",
                            lineNumber: 110,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 24,
                            md: 8,
                            lg: 8,
                            xl: 8,
                            xxl: 8,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                direction: "vertical",
                                size: "large",
                                style: {
                                    width: '100%'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                                        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PhoneOutlined, {
                                                    style: {
                                                        color: '#dc2626'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 225,
                                                    columnNumber: 21
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                    children: "联系我们"
                                                }, void 0, false, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 226,
                                                    columnNumber: 21
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/help/index.tsx",
                                            lineNumber: 224,
                                            columnNumber: 19
                                        }, void 0),
                                        headerBordered: true,
                                        style: {
                                            borderRadius: 12
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            direction: "vertical",
                                            size: "middle",
                                            style: {
                                                width: '100%'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        padding: '8px 0'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {
                                                            style: {
                                                                color: '#2563eb',
                                                                marginRight: 12,
                                                                fontSize: 16
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 234,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    strong: true,
                                                                    children: "技术支持邮箱"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/help/index.tsx",
                                                                    lineNumber: 236,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                                                    fileName: "src/pages/help/index.tsx",
                                                                    lineNumber: 237,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    type: "secondary",
                                                                    style: {
                                                                        fontSize: 12
                                                                    },
                                                                    children: "<EMAIL>"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/help/index.tsx",
                                                                    lineNumber: 238,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 235,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 233,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                                    style: {
                                                        margin: '8px 0'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 241,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        padding: '8px 0'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {
                                                            style: {
                                                                color: '#059669',
                                                                marginRight: 12,
                                                                fontSize: 16
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 243,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    strong: true,
                                                                    children: "用户反馈"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/help/index.tsx",
                                                                    lineNumber: 245,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                                                    fileName: "src/pages/help/index.tsx",
                                                                    lineNumber: 246,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    type: "secondary",
                                                                    style: {
                                                                        fontSize: 12
                                                                    },
                                                                    children: "<EMAIL>"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/help/index.tsx",
                                                                    lineNumber: 247,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 244,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 242,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {
                                                    style: {
                                                        margin: '8px 0'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 250,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        padding: '8px 0'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                            style: {
                                                                color: '#d97706',
                                                                marginRight: 12,
                                                                fontSize: 16
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 252,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    strong: true,
                                                                    children: "工作时间"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/help/index.tsx",
                                                                    lineNumber: 254,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                                                    fileName: "src/pages/help/index.tsx",
                                                                    lineNumber: 255,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    type: "secondary",
                                                                    style: {
                                                                        fontSize: 12
                                                                    },
                                                                    children: "周一至周五 9:00-18:00"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/help/index.tsx",
                                                                    lineNumber: 256,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 253,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 251,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/help/index.tsx",
                                            lineNumber: 232,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 222,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                                        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.QuestionCircleOutlined, {
                                                    style: {
                                                        color: '#0891b2'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 266,
                                                    columnNumber: 21
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                    children: "常见问题"
                                                }, void 0, false, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 267,
                                                    columnNumber: 21
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/help/index.tsx",
                                            lineNumber: 265,
                                            columnNumber: 19
                                        }, void 0),
                                        headerBordered: true,
                                        style: {
                                            borderRadius: 12
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            direction: "vertical",
                                            size: "large",
                                            style: {
                                                width: '100%'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                            level: 5,
                                                            style: {
                                                                color: '#2563eb',
                                                                marginBottom: 8
                                                            },
                                                            children: "Q: 如何切换团队？"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 275,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                                            style: {
                                                                color: '#6b7280',
                                                                marginBottom: 0
                                                            },
                                                            children: "A: 在顶部导航栏的团队名称处点击，可以选择切换到其他团队。"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 278,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 274,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 282,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                            level: 5,
                                                            style: {
                                                                color: '#2563eb',
                                                                marginBottom: 8
                                                            },
                                                            children: "Q: 忘记密码怎么办？"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 284,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                                            style: {
                                                                color: '#6b7280',
                                                                marginBottom: 0
                                                            },
                                                            children: "A: 在登录页面使用邮箱验证码登录，系统会自动处理身份验证。"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 287,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 283,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 291,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                            level: 5,
                                                            style: {
                                                                color: '#2563eb',
                                                                marginBottom: 8
                                                            },
                                                            children: "Q: 如何邀请新成员？"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 293,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                                            style: {
                                                                color: '#6b7280',
                                                                marginBottom: 0
                                                            },
                                                            children: "A: 团队管理员可以在团队管理页面通过邮箱邀请新成员。"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 296,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 292,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 300,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                            level: 5,
                                                            style: {
                                                                color: '#2563eb',
                                                                marginBottom: 8
                                                            },
                                                            children: "Q: 如何管理团队权限？"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 302,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                                            style: {
                                                                color: '#6b7280',
                                                                marginBottom: 0
                                                            },
                                                            children: "A: 团队管理员可以在团队设置中为不同成员分配不同的角色和权限。"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/help/index.tsx",
                                                            lineNumber: 305,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/help/index.tsx",
                                                    lineNumber: 301,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/help/index.tsx",
                                            lineNumber: 273,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 263,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 220,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/help/index.tsx",
                            lineNumber: 219,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/help/index.tsx",
                    lineNumber: 108,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/help/index.tsx",
            lineNumber: 31,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/help/index.tsx",
        lineNumber: 22,
        columnNumber: 5
    }, this);
};
_c = HelpPage;
var _default = HelpPage;
var _c;
$RefreshReg$(_c, "HelpPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__help__index-async.js.map