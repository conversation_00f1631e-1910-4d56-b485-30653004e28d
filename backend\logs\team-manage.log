2025-08-05 12:25:26.607 [background-preinit] INFO  o.h.validator.internal.util.Version -- HV000001: Hibernate Validator 8.0.2.Final
2025-08-05 12:25:26.701 [main] INFO  c.teammanage.TeamManageApplication -- Starting TeamManageApplication using Java 17.0.8 with PID 15820 (H:\projects\IdeaProjects\teamAuth\backend\target\classes started by sweetotoro in H:\projects\IdeaProjects\teamAuth\backend)
2025-08-05 12:25:26.705 [main] INFO  c.teammanage.TeamManageApplication -- No active profile set, falling back to 1 default profile: "default"
2025-08-05 12:25:29.856 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer -- Tomcat initialized with port 8080 (http)
2025-08-05 12:25:29.894 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Initializing ProtocolHandler ["http-nio-8080"]
2025-08-05 12:25:29.899 [main] INFO  o.a.catalina.core.StandardService -- Starting service [Tomcat]
2025-08-05 12:25:29.899 [main] INFO  o.a.catalina.core.StandardEngine -- Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-05 12:25:30.114 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] -- Initializing Spring embedded WebApplicationContext
2025-08-05 12:25:30.115 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext -- Root WebApplicationContext: initialization completed in 3306 ms
2025-08-05 12:25:32.326 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration -- 

Using generated security password: 016760eb-f750-4022-a170-b9dec93da420

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-05 12:25:32.662 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware -- Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@160ac7fb
2025-08-05 12:25:32.888 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Starting ProtocolHandler ["http-nio-8080"]
2025-08-05 12:25:32.922 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext -- Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-05 12:25:32.946 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger -- 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-05 12:25:32.966 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter -- 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-08-05 12:26:27.995 [background-preinit] INFO  o.h.validator.internal.util.Version -- HV000001: Hibernate Validator 8.0.2.Final
2025-08-05 12:26:28.101 [main] INFO  c.teammanage.TeamManageApplication -- Starting TeamManageApplication using Java 17.0.8 with PID 6812 (H:\projects\IdeaProjects\teamAuth\backend\target\classes started by sweetotoro in H:\projects\IdeaProjects\teamAuth\backend)
2025-08-05 12:26:28.105 [main] INFO  c.teammanage.TeamManageApplication -- No active profile set, falling back to 1 default profile: "default"
2025-08-05 12:26:30.684 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer -- Tomcat initialized with port 8080 (http)
2025-08-05 12:26:30.709 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Initializing ProtocolHandler ["http-nio-8080"]
2025-08-05 12:26:30.714 [main] INFO  o.a.catalina.core.StandardService -- Starting service [Tomcat]
2025-08-05 12:26:30.715 [main] INFO  o.a.catalina.core.StandardEngine -- Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-05 12:26:30.852 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] -- Initializing Spring embedded WebApplicationContext
2025-08-05 12:26:30.853 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext -- Root WebApplicationContext: initialization completed in 2646 ms
2025-08-05 12:26:32.929 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration -- 

Using generated security password: 1f188941-e8c0-4553-8449-9b1c8ecc6013

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-05 12:26:33.386 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware -- Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@160ac7fb
2025-08-05 12:26:33.693 [main] INFO  o.a.coyote.http11.Http11NioProtocol -- Starting ProtocolHandler ["http-nio-8080"]
2025-08-05 12:26:33.707 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext -- Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-05 12:26:33.739 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger -- 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-05 12:26:33.766 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter -- 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

