{"version": 3, "sources": ["src/pages/Dashboard/index.tsx"], "sourcesContent": ["/**\n * 仪表板页面\n *\n * 企业级仪表板页面，提供数据概览、快速操作和系统状态监控。\n * 采用响应式设计，适配不同屏幕尺寸，使用专业商务配色方案。\n */\n\nimport { PageContainer, ProCard, StatisticCard } from '@ant-design/pro-components';\nimport { Row, Col, Typography, Space, Button, Divider } from 'antd';\nimport {\n  DashboardOutlined,\n  TeamOutlined,\n  UserOutlined,\n  SettingOutlined,\n  BarChartOutlined,\n  BellOutlined,\n  FileTextOutlined,\n  TrophyOutlined\n} from '@ant-design/icons';\nimport React from 'react';\n\nconst { Title, Text, Paragraph } = Typography;\n\nconst Dashboard: React.FC = () => {\n  return (\n    <PageContainer\n      title=\"仪表板\"\n      subTitle=\"团队协作管理系统概览\"\n      extra={[\n        <Button key=\"refresh\" icon={<BarChartOutlined />}>\n          刷新数据\n        </Button>,\n        <Button key=\"settings\" icon={<SettingOutlined />} type=\"primary\">\n          系统设置\n        </Button>,\n      ]}\n    >\n      <div style={{ padding: '0 24px' }}>\n        {/* 欢迎区域 */}\n        <ProCard\n          style={{\n            marginBottom: 24,\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            color: 'white',\n            borderRadius: 16,\n          }}\n          bodyStyle={{ padding: '32px' }}\n        >\n          <Row gutter={[24, 24]} align=\"middle\">\n            <Col xs={24} sm={24} md={16} lg={18} xl={18} xxl={18}>\n              <Space direction=\"vertical\" size=\"small\">\n                <Title level={2} style={{ color: 'white', margin: 0 }}>\n                  <DashboardOutlined style={{ marginRight: 12 }} />\n                  欢迎使用团队协作管理系统\n                </Title>\n                <Paragraph style={{ color: 'rgba(255, 255, 255, 0.9)', margin: 0, fontSize: 16 }}>\n                  高效管理团队，提升协作效率，让每个项目都能成功交付\n                </Paragraph>\n              </Space>\n            </Col>\n            <Col xs={24} sm={24} md={8} lg={6} xl={6} xxl={6}>\n              <div style={{ textAlign: 'center' }}>\n                <TrophyOutlined style={{ fontSize: 64, color: 'rgba(255, 255, 255, 0.8)' }} />\n              </div>\n            </Col>\n          </Row>\n        </ProCard>\n\n        {/* 统计卡片区域 */}\n        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n          <Col xs={12} sm={12} md={6} lg={6} xl={6} xxl={6}>\n            <StatisticCard\n              statistic={{\n                title: '团队数量',\n                value: 8,\n                icon: <TeamOutlined style={{ color: '#2563eb' }} />,\n              }}\n              style={{\n                background: 'linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%)',\n                borderRadius: 12,\n              }}\n            />\n          </Col>\n          <Col xs={12} sm={12} md={6} lg={6} xl={6} xxl={6}>\n            <StatisticCard\n              statistic={{\n                title: '团队成员',\n                value: 156,\n                icon: <UserOutlined style={{ color: '#059669' }} />,\n              }}\n              style={{\n                background: 'linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%)',\n                borderRadius: 12,\n              }}\n            />\n          </Col>\n          <Col xs={12} sm={12} md={6} lg={6} xl={6} xxl={6}>\n            <StatisticCard\n              statistic={{\n                title: '活跃项目',\n                value: 24,\n                icon: <FileTextOutlined style={{ color: '#d97706' }} />,\n              }}\n              style={{\n                background: 'linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%)',\n                borderRadius: 12,\n              }}\n            />\n          </Col>\n          <Col xs={12} sm={12} md={6} lg={6} xl={6} xxl={6}>\n            <StatisticCard\n              statistic={{\n                title: '待处理任务',\n                value: 42,\n                icon: <BellOutlined style={{ color: '#dc2626' }} />,\n              }}\n              style={{\n                background: 'linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)',\n                borderRadius: 12,\n              }}\n            />\n          </Col>\n        </Row>\n\n        {/* 主要功能区域 */}\n        <Row gutter={[24, 24]}>\n          {/* 快速操作 */}\n          <Col xs={24} sm={24} md={12} lg={8} xl={8} xxl={8}>\n            <ProCard\n              title=\"快速操作\"\n              headerBordered\n              style={{ height: '100%', borderRadius: 12 }}\n            >\n              <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n                <Button\n                  type=\"primary\"\n                  icon={<TeamOutlined />}\n                  size=\"large\"\n                  block\n                  style={{ height: 48, borderRadius: 8 }}\n                >\n                  创建新团队\n                </Button>\n                <Button\n                  icon={<UserOutlined />}\n                  size=\"large\"\n                  block\n                  style={{ height: 48, borderRadius: 8 }}\n                >\n                  邀请成员\n                </Button>\n                <Button\n                  icon={<FileTextOutlined />}\n                  size=\"large\"\n                  block\n                  style={{ height: 48, borderRadius: 8 }}\n                >\n                  创建项目\n                </Button>\n                <Divider style={{ margin: '12px 0' }} />\n                <Button\n                  icon={<SettingOutlined />}\n                  size=\"large\"\n                  block\n                  type=\"dashed\"\n                  style={{ height: 48, borderRadius: 8 }}\n                >\n                  系统设置\n                </Button>\n              </Space>\n            </ProCard>\n          </Col>\n\n          {/* 最近活动 */}\n          <Col xs={24} sm={24} md={12} lg={8} xl={8} xxl={8}>\n            <ProCard\n              title=\"最近活动\"\n              headerBordered\n              style={{ height: '100%', borderRadius: 12 }}\n            >\n              <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n                <div style={{ padding: '12px 0', borderBottom: '1px solid #f0f0f0' }}>\n                  <Text strong>张三</Text>\n                  <Text type=\"secondary\" style={{ marginLeft: 8 }}>加入了团队</Text>\n                  <br />\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>2 小时前</Text>\n                </div>\n                <div style={{ padding: '12px 0', borderBottom: '1px solid #f0f0f0' }}>\n                  <Text strong>李四</Text>\n                  <Text type=\"secondary\" style={{ marginLeft: 8 }}>完成了任务</Text>\n                  <br />\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>4 小时前</Text>\n                </div>\n                <div style={{ padding: '12px 0', borderBottom: '1px solid #f0f0f0' }}>\n                  <Text strong>王五</Text>\n                  <Text type=\"secondary\" style={{ marginLeft: 8 }}>创建了新项目</Text>\n                  <br />\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>1 天前</Text>\n                </div>\n                <div style={{ padding: '12px 0' }}>\n                  <Text strong>赵六</Text>\n                  <Text type=\"secondary\" style={{ marginLeft: 8 }}>更新了文档</Text>\n                  <br />\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>2 天前</Text>\n                </div>\n              </Space>\n            </ProCard>\n          </Col>\n\n          {/* 系统状态 */}\n          <Col xs={24} sm={24} md={24} lg={8} xl={8} xxl={8}>\n            <ProCard\n              title=\"系统状态\"\n              headerBordered\n              style={{ height: '100%', borderRadius: 12 }}\n            >\n              <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                  <Text>服务器状态</Text>\n                  <div style={{ display: 'flex', alignItems: 'center' }}>\n                    <div style={{\n                      width: 8,\n                      height: 8,\n                      borderRadius: '50%',\n                      backgroundColor: '#059669',\n                      marginRight: 8\n                    }} />\n                    <Text type=\"success\">正常</Text>\n                  </div>\n                </div>\n                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                  <Text>数据库连接</Text>\n                  <div style={{ display: 'flex', alignItems: 'center' }}>\n                    <div style={{\n                      width: 8,\n                      height: 8,\n                      borderRadius: '50%',\n                      backgroundColor: '#059669',\n                      marginRight: 8\n                    }} />\n                    <Text type=\"success\">正常</Text>\n                  </div>\n                </div>\n                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                  <Text>存储空间</Text>\n                  <Text>78% 已使用</Text>\n                </div>\n                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                  <Text>在线用户</Text>\n                  <Text strong style={{ color: '#2563eb' }}>24 人</Text>\n                </div>\n                <Divider style={{ margin: '12px 0' }} />\n                <div style={{ textAlign: 'center' }}>\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    最后更新：刚刚\n                  </Text>\n                </div>\n              </Space>\n            </ProCard>\n          </Col>\n        </Row>\n      </div>\n    </PageContainer>\n  );\n};\n\nexport default Dashboard;\n"], "names": [], "mappings": ";;;AAAA;;;;;CAKC;;;;4BAqQD;;;eAAA;;;;;;;sCAnQsD;6BACO;8BAUtD;uEACW;;;;;;;;;AAElB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;AAE7C,MAAM,YAAsB;IAC1B,qBACE,2BAAC,4BAAa;QACZ,OAAM;QACN,UAAS;QACT,OAAO;0BACL,2BAAC,YAAM;gBAAe,oBAAM,2BAAC,uBAAgB;;;;;0BAAK;eAAtC;;;;;0BAGZ,2BAAC,YAAM;gBAAgB,oBAAM,2BAAC,sBAAe;;;;;gBAAK,MAAK;0BAAU;eAArD;;;;;SAGb;kBAED,cAAA,2BAAC;YAAI,OAAO;gBAAE,SAAS;YAAS;;8BAE9B,2BAAC,sBAAO;oBACN,OAAO;wBACL,cAAc;wBACd,YAAY;wBACZ,OAAO;wBACP,cAAc;oBAChB;oBACA,WAAW;wBAAE,SAAS;oBAAO;8BAE7B,cAAA,2BAAC,SAAG;wBAAC,QAAQ;4BAAC;4BAAI;yBAAG;wBAAE,OAAM;;0CAC3B,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAI,KAAK;0CAChD,cAAA,2BAAC,WAAK;oCAAC,WAAU;oCAAW,MAAK;;sDAC/B,2BAAC;4CAAM,OAAO;4CAAG,OAAO;gDAAE,OAAO;gDAAS,QAAQ;4CAAE;;8DAClD,2BAAC,wBAAiB;oDAAC,OAAO;wDAAE,aAAa;oDAAG;;;;;;gDAAK;;;;;;;sDAGnD,2BAAC;4CAAU,OAAO;gDAAE,OAAO;gDAA4B,QAAQ;gDAAG,UAAU;4CAAG;sDAAG;;;;;;;;;;;;;;;;;0CAKtF,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;gCAAG,IAAI;gCAAG,KAAK;0CAC7C,cAAA,2BAAC;oCAAI,OAAO;wCAAE,WAAW;oCAAS;8CAChC,cAAA,2BAAC,qBAAc;wCAAC,OAAO;4CAAE,UAAU;4CAAI,OAAO;wCAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOjF,2BAAC,SAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;oBAAE,OAAO;wBAAE,cAAc;oBAAG;;sCAC/C,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAG,IAAI;4BAAG,IAAI;4BAAG,KAAK;sCAC7C,cAAA,2BAAC,4BAAa;gCACZ,WAAW;oCACT,OAAO;oCACP,OAAO;oCACP,oBAAM,2BAAC,mBAAY;wCAAC,OAAO;4CAAE,OAAO;wCAAU;;;;;;gCAChD;gCACA,OAAO;oCACL,YAAY;oCACZ,cAAc;gCAChB;;;;;;;;;;;sCAGJ,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAG,IAAI;4BAAG,IAAI;4BAAG,KAAK;sCAC7C,cAAA,2BAAC,4BAAa;gCACZ,WAAW;oCACT,OAAO;oCACP,OAAO;oCACP,oBAAM,2BAAC,mBAAY;wCAAC,OAAO;4CAAE,OAAO;wCAAU;;;;;;gCAChD;gCACA,OAAO;oCACL,YAAY;oCACZ,cAAc;gCAChB;;;;;;;;;;;sCAGJ,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAG,IAAI;4BAAG,IAAI;4BAAG,KAAK;sCAC7C,cAAA,2BAAC,4BAAa;gCACZ,WAAW;oCACT,OAAO;oCACP,OAAO;oCACP,oBAAM,2BAAC,uBAAgB;wCAAC,OAAO;4CAAE,OAAO;wCAAU;;;;;;gCACpD;gCACA,OAAO;oCACL,YAAY;oCACZ,cAAc;gCAChB;;;;;;;;;;;sCAGJ,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAG,IAAI;4BAAG,IAAI;4BAAG,KAAK;sCAC7C,cAAA,2BAAC,4BAAa;gCACZ,WAAW;oCACT,OAAO;oCACP,OAAO;oCACP,oBAAM,2BAAC,mBAAY;wCAAC,OAAO;4CAAE,OAAO;wCAAU;;;;;;gCAChD;gCACA,OAAO;oCACL,YAAY;oCACZ,cAAc;gCAChB;;;;;;;;;;;;;;;;;8BAMN,2BAAC,SAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;;sCAEnB,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAG,IAAI;4BAAG,KAAK;sCAC9C,cAAA,2BAAC,sBAAO;gCACN,OAAM;gCACN,cAAc;gCACd,OAAO;oCAAE,QAAQ;oCAAQ,cAAc;gCAAG;0CAE1C,cAAA,2BAAC,WAAK;oCAAC,WAAU;oCAAW,MAAK;oCAAS,OAAO;wCAAE,OAAO;oCAAO;;sDAC/D,2BAAC,YAAM;4CACL,MAAK;4CACL,oBAAM,2BAAC,mBAAY;;;;;4CACnB,MAAK;4CACL,KAAK;4CACL,OAAO;gDAAE,QAAQ;gDAAI,cAAc;4CAAE;sDACtC;;;;;;sDAGD,2BAAC,YAAM;4CACL,oBAAM,2BAAC,mBAAY;;;;;4CACnB,MAAK;4CACL,KAAK;4CACL,OAAO;gDAAE,QAAQ;gDAAI,cAAc;4CAAE;sDACtC;;;;;;sDAGD,2BAAC,YAAM;4CACL,oBAAM,2BAAC,uBAAgB;;;;;4CACvB,MAAK;4CACL,KAAK;4CACL,OAAO;gDAAE,QAAQ;gDAAI,cAAc;4CAAE;sDACtC;;;;;;sDAGD,2BAAC,aAAO;4CAAC,OAAO;gDAAE,QAAQ;4CAAS;;;;;;sDACnC,2BAAC,YAAM;4CACL,oBAAM,2BAAC,sBAAe;;;;;4CACtB,MAAK;4CACL,KAAK;4CACL,MAAK;4CACL,OAAO;gDAAE,QAAQ;gDAAI,cAAc;4CAAE;sDACtC;;;;;;;;;;;;;;;;;;;;;;sCAQP,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAG,IAAI;4BAAG,KAAK;sCAC9C,cAAA,2BAAC,sBAAO;gCACN,OAAM;gCACN,cAAc;gCACd,OAAO;oCAAE,QAAQ;oCAAQ,cAAc;gCAAG;0CAE1C,cAAA,2BAAC,WAAK;oCAAC,WAAU;oCAAW,MAAK;oCAAS,OAAO;wCAAE,OAAO;oCAAO;;sDAC/D,2BAAC;4CAAI,OAAO;gDAAE,SAAS;gDAAU,cAAc;4CAAoB;;8DACjE,2BAAC;oDAAK,MAAM;8DAAC;;;;;;8DACb,2BAAC;oDAAK,MAAK;oDAAY,OAAO;wDAAE,YAAY;oDAAE;8DAAG;;;;;;8DACjD,2BAAC;;;;;8DACD,2BAAC;oDAAK,MAAK;oDAAY,OAAO;wDAAE,UAAU;oDAAG;8DAAG;;;;;;;;;;;;sDAElD,2BAAC;4CAAI,OAAO;gDAAE,SAAS;gDAAU,cAAc;4CAAoB;;8DACjE,2BAAC;oDAAK,MAAM;8DAAC;;;;;;8DACb,2BAAC;oDAAK,MAAK;oDAAY,OAAO;wDAAE,YAAY;oDAAE;8DAAG;;;;;;8DACjD,2BAAC;;;;;8DACD,2BAAC;oDAAK,MAAK;oDAAY,OAAO;wDAAE,UAAU;oDAAG;8DAAG;;;;;;;;;;;;sDAElD,2BAAC;4CAAI,OAAO;gDAAE,SAAS;gDAAU,cAAc;4CAAoB;;8DACjE,2BAAC;oDAAK,MAAM;8DAAC;;;;;;8DACb,2BAAC;oDAAK,MAAK;oDAAY,OAAO;wDAAE,YAAY;oDAAE;8DAAG;;;;;;8DACjD,2BAAC;;;;;8DACD,2BAAC;oDAAK,MAAK;oDAAY,OAAO;wDAAE,UAAU;oDAAG;8DAAG;;;;;;;;;;;;sDAElD,2BAAC;4CAAI,OAAO;gDAAE,SAAS;4CAAS;;8DAC9B,2BAAC;oDAAK,MAAM;8DAAC;;;;;;8DACb,2BAAC;oDAAK,MAAK;oDAAY,OAAO;wDAAE,YAAY;oDAAE;8DAAG;;;;;;8DACjD,2BAAC;;;;;8DACD,2BAAC;oDAAK,MAAK;oDAAY,OAAO;wDAAE,UAAU;oDAAG;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxD,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAG,IAAI;4BAAG,KAAK;sCAC9C,cAAA,2BAAC,sBAAO;gCACN,OAAM;gCACN,cAAc;gCACd,OAAO;oCAAE,QAAQ;oCAAQ,cAAc;gCAAG;0CAE1C,cAAA,2BAAC,WAAK;oCAAC,WAAU;oCAAW,MAAK;oCAAS,OAAO;wCAAE,OAAO;oCAAO;;sDAC/D,2BAAC;4CAAI,OAAO;gDAAE,SAAS;gDAAQ,gBAAgB;gDAAiB,YAAY;4CAAS;;8DACnF,2BAAC;8DAAK;;;;;;8DACN,2BAAC;oDAAI,OAAO;wDAAE,SAAS;wDAAQ,YAAY;oDAAS;;sEAClD,2BAAC;4DAAI,OAAO;gEACV,OAAO;gEACP,QAAQ;gEACR,cAAc;gEACd,iBAAiB;gEACjB,aAAa;4DACf;;;;;;sEACA,2BAAC;4DAAK,MAAK;sEAAU;;;;;;;;;;;;;;;;;;sDAGzB,2BAAC;4CAAI,OAAO;gDAAE,SAAS;gDAAQ,gBAAgB;gDAAiB,YAAY;4CAAS;;8DACnF,2BAAC;8DAAK;;;;;;8DACN,2BAAC;oDAAI,OAAO;wDAAE,SAAS;wDAAQ,YAAY;oDAAS;;sEAClD,2BAAC;4DAAI,OAAO;gEACV,OAAO;gEACP,QAAQ;gEACR,cAAc;gEACd,iBAAiB;gEACjB,aAAa;4DACf;;;;;;sEACA,2BAAC;4DAAK,MAAK;sEAAU;;;;;;;;;;;;;;;;;;sDAGzB,2BAAC;4CAAI,OAAO;gDAAE,SAAS;gDAAQ,gBAAgB;gDAAiB,YAAY;4CAAS;;8DACnF,2BAAC;8DAAK;;;;;;8DACN,2BAAC;8DAAK;;;;;;;;;;;;sDAER,2BAAC;4CAAI,OAAO;gDAAE,SAAS;gDAAQ,gBAAgB;gDAAiB,YAAY;4CAAS;;8DACnF,2BAAC;8DAAK;;;;;;8DACN,2BAAC;oDAAK,MAAM;oDAAC,OAAO;wDAAE,OAAO;oDAAU;8DAAG;;;;;;;;;;;;sDAE5C,2BAAC,aAAO;4CAAC,OAAO;gDAAE,QAAQ;4CAAS;;;;;;sDACnC,2BAAC;4CAAI,OAAO;gDAAE,WAAW;4CAAS;sDAChC,cAAA,2BAAC;gDAAK,MAAK;gDAAY,OAAO;oDAAE,UAAU;gDAAG;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWlE;KAjPM;IAmPN,WAAe"}