@font-face{
  font-family: "AlibabaSans";
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("//mdn.alipayobjects.com/huamei_iwk9zp/afts/file/A*1GSgSYDD_aIAAAAAQsAAAAgAegCCAQ/AlibabaSans-Light.woff2") format("woff2");
}
@font-face{
  font-family: "AlibabaSans";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("//mdn.alipayobjects.com/huamei_iwk9zp/afts/file/A*2zEUQqnPNesAAAAAQtAAAAgAegCCAQ/AlibabaSans-Regular.woff2") format("woff2");
}
@font-face{
  font-family: "AlibabaSans";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("//mdn.alipayobjects.com/huamei_iwk9zp/afts/file/A*E_cxRbMlZqUAAAAAQuAAAAgAegCCAQ/AlibabaSans-Medium.woff2") format("woff2");
}
@font-face{
  font-family: "AlibabaSans";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("//mdn.alipayobjects.com/huamei_iwk9zp/afts/file/A*E_cxRbMlZqUAAAAAQuAAAAgAegCCAQ/AlibabaSans-Bold.woff2") format("woff2");
}
@font-face{
  font-family: "AlibabaSans";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("//mdn.alipayobjects.com/huamei_iwk9zp/afts/file/A*E_cxRbMlZqUAAAAAQuAAAAgAegCCAQ/AlibabaSans-Heavy.woff2") format("woff2");
}
html, 
body, 
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: AlibabaSans, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
}
*, 
*::before, 
*::after {
  box-sizing: border-box;
}
.colorWeak {
  filter: invert(80%);
}
.ant-layout {
  min-height: 100vh;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
}
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}
canvas {
  display: block;
}
body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
ul, 
ol {
  list-style: none;
}
@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
  }
  .ant-table-thead > tr > th, 
  .ant-table-tbody > tr > th, 
  .ant-table-thead > tr > td, 
  .ant-table-tbody > tr > td {
    white-space: pre;
  }
  .ant-table-thead > tr > th > span, 
  .ant-table-tbody > tr > th > span, 
  .ant-table-thead > tr > td > span, 
  .ant-table-tbody > tr > td > span {
    display: block;
  }
}
.ant-pro-layout, 
.ant-pro-layout-content, 
.ant-layout-content {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
}
.ant-pro-page-container, 
.ant-pro-page-container-children-content {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}
.ant-table-wrapper {
  width: 100%;
  overflow-x: auto;
}
.ant-modal, 
.ant-drawer {
  max-width: 100vw;
}
:root {
  --primary-color: #2563eb;
  --primary-color-hover: #3b82f6;
  --primary-color-active: #1d4ed8;
  --success-color: #059669;
  --warning-color: #d97706;
  --error-color: #dc2626;
  --info-color: #0891b2;
  --text-color: #1f2937;
  --text-color-secondary: #6b7280;
  --text-color-tertiary: #9ca3af;
  --border-color: #e5e7eb;
  --border-color-secondary: #f3f4f6;
  --bg-container: #ffffff;
  --bg-layout: #f8fafc;
  --bg-spotlight: #f1f5f9;
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
}
.ant-card {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color-secondary);
  transition: all 0.3s ease;
}
.ant-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-color);
}
.ant-card .ant-card-head {
  border-bottom: 1px solid var(--border-color-secondary);
  background: var(--bg-container);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}
.ant-card .ant-card-head .ant-card-head-title {
  color: var(--text-color);
  font-weight: 600;
}
.ant-card .ant-card-body {
  background: var(--bg-container);
}
.ant-btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: all 0.3s ease;
}
.ant-btn.ant-btn-primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
}
.ant-btn.ant-btn-primary:hover {
  background: var(--primary-color-hover);
  border-color: var(--primary-color-hover);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}
.ant-btn.ant-btn-primary:active {
  background: var(--primary-color-active);
  border-color: var(--primary-color-active);
}
.ant-table {
  border-radius: var(--border-radius);
  overflow: hidden;
}
.ant-table .ant-table-thead > tr > th {
  background: var(--bg-spotlight);
  color: var(--text-color);
  font-weight: 600;
  border-bottom: 1px solid var(--border-color);
}
.ant-table .ant-table-tbody > tr:hover > td {
  background: var(--bg-spotlight);
}
.ant-input, 
.ant-select-selector {
  border-radius: var(--border-radius);
  border-color: var(--border-color);
}
.ant-input:hover, 
.ant-select-selector:hover {
  border-color: var(--primary-color-hover);
}
.ant-input:focus, 
.ant-select-selector:focus, 
.ant-input.ant-select-focused .ant-select-selector, 
.ant-select-selector.ant-select-focused .ant-select-selector {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}
.ant-modal .ant-modal-content {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}
.ant-modal .ant-modal-header {
  background: var(--bg-container);
  border-bottom: 1px solid var(--border-color-secondary);
}
.ant-modal .ant-modal-header .ant-modal-title {
  color: var(--text-color);
  font-weight: 600;
}
.ant-modal .ant-modal-body {
  background: var(--bg-container);
}
.ant-modal .ant-modal-footer {
  background: var(--bg-container);
  border-top: 1px solid var(--border-color-secondary);
}
.ant-menu {
  border-radius: var(--border-radius);
}
.ant-menu .ant-menu-item, 
.ant-menu .ant-menu-submenu-title {
  border-radius: 6px;
  margin: 2px 8px;
  width: calc(100% - 16px);
}
.ant-menu .ant-menu-item:hover, 
.ant-menu .ant-menu-submenu-title:hover {
  background: var(--bg-spotlight);
}
.ant-menu .ant-menu-item.ant-menu-item-selected, 
.ant-menu .ant-menu-submenu-title.ant-menu-item-selected {
  background: rgba(37, 99, 235, 0.1);
  color: var(--primary-color);
}
.ant-tabs .ant-tabs-tab {
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}
.ant-tabs .ant-tabs-tab.ant-tabs-tab-active {
  background: var(--bg-container);
}
.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: var(--primary-color);
  font-weight: 600;
}
.ant-tabs .ant-tabs-content-holder {
  background: var(--bg-container);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}
@media (max-width: 576px) {
  .ant-card {
    margin: 8px;
    border-radius: var(--border-radius);
  }
  .ant-modal {
    margin: 16px;
  }
  .ant-modal .ant-modal-content {
    border-radius: var(--border-radius);
  }
  .ant-btn {
    height: 36px;
    padding: 0 12px;
    font-size: 14px;
  }
}
@media (min-width: 1200px) {
  .ant-card {
    border-radius: var(--border-radius-xl);
  }
  .ant-modal .ant-modal-content {
    border-radius: var(--border-radius-xl);
  }
}
html, 
body {
  width: 100%;
  height: 100%;
}
input::-ms-clear, 
input::-ms-reveal {
  display: none;
}
*, 
*::before, 
*::after {
  box-sizing: border-box;
}
html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
@-ms-viewport{
  width: device-width;
}
body {
  margin: 0;
}
[tabindex='-1']:focus {
  outline: none;
}
hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}
h1, 
h2, 
h3, 
h4, 
h5, 
h6 {
  margin-top: 0;
  margin-bottom: 0.5em;
  font-weight: 500;
}
p {
  margin-top: 0;
  margin-bottom: 1em;
}
abbr[title], 
abbr[data-original-title] {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
  border-bottom: 0;
  cursor: help;
}
address {
  margin-bottom: 1em;
  font-style: normal;
  line-height: inherit;
}
input[type='text'], 
input[type='password'], 
input[type='number'], 
textarea {
  -webkit-appearance: none;
}
ol, 
ul, 
dl {
  margin-top: 0;
  margin-bottom: 1em;
}
ol ol, 
ul ul, 
ol ul, 
ul ol {
  margin-bottom: 0;
}
dt {
  font-weight: 500;
}
dd {
  margin-bottom: 0.5em;
  margin-left: 0;
}
blockquote {
  margin: 0 0 1em;
}
dfn {
  font-style: italic;
}
b, 
strong {
  font-weight: bolder;
}
small {
  font-size: 80%;
}
sub, 
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
pre, 
code, 
kbd, 
samp {
  font-size: 1em;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}
pre {
  margin-top: 0;
  margin-bottom: 1em;
  overflow: auto;
}
figure {
  margin: 0 0 1em;
}
img {
  vertical-align: middle;
  border-style: none;
}
a, 
area, 
button, 
[role='button'], 
input:not([type='range']), 
label, 
select, 
summary, 
textarea {
  touch-action: manipulation;
}
table {
  border-collapse: collapse;
}
caption {
  padding-top: 0.75em;
  padding-bottom: 0.3em;
  text-align: left;
  caption-side: bottom;
}
input, 
button, 
select, 
optgroup, 
textarea {
  margin: 0;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}
button, 
input {
  overflow: visible;
}
button, 
select {
  text-transform: none;
}
button, 
html [type='button'], 
[type='reset'], 
[type='submit'] {
  -webkit-appearance: button;
}
button::-moz-focus-inner, 
[type='button']::-moz-focus-inner, 
[type='reset']::-moz-focus-inner, 
[type='submit']::-moz-focus-inner {
  padding: 0;
  border-style: none;
}
input[type='radio'], 
input[type='checkbox'] {
  box-sizing: border-box;
  padding: 0;
}
input[type='date'], 
input[type='time'], 
input[type='datetime-local'], 
input[type='month'] {
  -webkit-appearance: listbox;
}
textarea {
  overflow: auto;
  resize: vertical;
}
fieldset {
  min-width: 0;
  margin: 0;
  padding: 0;
  border: 0;
}
legend {
  display: block;
  width: 100%;
  max-width: 100%;
  margin-bottom: 0.5em;
  padding: 0;
  color: inherit;
  font-size: 1.5em;
  line-height: inherit;
  white-space: normal;
}
progress {
  vertical-align: baseline;
}
[type='number']::-webkit-inner-spin-button, 
[type='number']::-webkit-outer-spin-button {
  height: auto;
}
[type='search'] {
  outline-offset: -2px;
  -webkit-appearance: none;
}
[type='search']::-webkit-search-cancel-button, 
[type='search']::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}
output {
  display: inline-block;
}
summary {
  display: list-item;
}
template {
  display: none;
}
[hidden] {
  display: none !important;
}
mark {
  padding: 0.2em;
  background-color: #feffe6;
}
/*# sourceMappingURL=umi.css.map*/